/* pages/acceptAnalysis/index.wxss */

/* 继承全局样式 */
@import "../../app.wxss";

/* 页面特定样式 */
.view-d {
  background-color: #fff;
  border-radius: 10rpx;
  margin: 10rpx 0;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 图表容器样式 */
.chart-container {
  height: 600rpx;
  width: 100%;
}

/* 进度条样式优化 */
progress {
  border-radius: 10rpx;
}

/* 数据项样式 */
.data-item {
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.data-item:last-child {
  border-bottom: none;
}

/* 标题样式 */
.section-title {
  display: flex;
  align-items: center;
  margin: 20rpx 0 10rpx 0;
  font-size: 26rpx;
  color: #666;
}

/* 加载状态样式 */
.loading-container {
  padding: 40rpx 0;
  text-align: center;
}

/* 空状态样式 */
.empty-container {
  padding: 40rpx 0;
  text-align: center;
}

/* 选择器样式 */
.selector-container {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 网格项样式 */
.grid-item {
  text-align: center;
  padding: 20rpx 10rpx;
}

.grid-item-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.grid-item-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.grid-item-rate {
  font-size: 22rpx;
  color: #999;
}

/* 统计区域样式 */
.stats-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.stats-controls {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

/* 响应式布局 */
@media (max-width: 750rpx) {
  .flex-item-2 {
    flex: 2;
  }
  
  .flex-item-3 {
    flex: 3;
  }
  
  .flex-item-5 {
    flex: 5;
  }
  
  .flex-item-6 {
    flex: 6;
  }
  
  .flex-item-7 {
    flex: 7;
  }
}

/* 时间选择器样式 */
.time-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 20rpx;
  background-color: #fff;
}

.time-item {
  padding: 0 10rpx;
  color: #333;
  font-size: 28rpx;
}

.time-separator {
  color: #999;
  margin: 0 10rpx;
}

/* Tab样式优化 */
.custom-tab {
  font-size: 24rpx !important;
}

/* 图表加载样式 */
.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}
