/**
 * 工单分析服务文件
 * 统一处理所有工单分析相关的API调用和数据处理
 */

const request = require("../../utils/request.js");
const rolePermission = require("../../utils/rolePermission.js");
const diyecharts = require("../../utils/diyecharts.js");
const config = require("./workorderConfig.js");

/**
 * 通用数据处理工具
 */
const DataProcessor = {
  /**
   * 处理百分比数据
   * @param {Array} data - 原始数据数组
   * @returns {Array} 处理后的数据数组
   */
  processPercentageData(data) {
    return data
      .filter((item) => item.label !== null) // 过滤掉label为null的项
      .map((item) => {
        // 计算百分比（用于进度条，需要是数值）
        const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
        return {
          ...item,
          // 数值形式的百分比，用于进度条
          ratePercent: ratePercentValue,
        };
      });
  },

  /**
   * 处理饼图数据
   * @param {Array} data - 原始数据数组
   * @param {number} analysisType - 分析类型
   * @returns {Array} 处理后的饼图数据
   */
  processPieData(data, analysisType) {
    const specialField = config.SPECIAL_DATA_FIELDS[analysisType];
    const valueField = specialField?.summaryValueField || 'quantity';

    return data
      .filter((e) => e.pieShowFlag) // 确保过滤有效数据
      .map((e) => ({
        value: e[valueField],
        name: e.title,
      }));
  },

  /**
   * 处理折线图数据
   * @param {Array} data - 原始数据数组
   * @param {number} timeType - 时间类型
   * @returns {Object} 包含linedata和xAxisData的对象
   */
  processLineData(data, timeType) {
    const linedata = [];
    const xAxisData = [];

    if (timeType == 2) {
      // 自然月处理
      const allMonths = Array.from({ length: 12 }, (_, i) => (i + 1).toString());
      const monthData = {};

      // 初始化所有月份的数据为0
      allMonths.forEach((month) => {
        monthData[month] = 0;
      });

      // 填充已有的月份数据
      data.forEach((e) => {
        let month = e.label;
        if (month.includes("-")) {
          month = month.split("-")[1];
        }
        month = parseInt(month).toString();
        let rate = parseFloat((e.rate * 100).toFixed(12));
        monthData[month] = rate;
      });

      // 按月份顺序填充数据
      allMonths.forEach((month) => {
        xAxisData.push(month + "月");
        linedata.push(monthData[month]);
      });
    } else if (timeType == 3) {
      // 自然周处理
      const weekData = [];
      data.forEach((e) => {
        let week = e.label;
        if (week.includes("-")) {
          week = week.split("-")[1];
        }
        week = parseInt(week).toString();
        let rate = parseFloat((e.rate * 100).toFixed(12));
        weekData.push({ week: week, rate: rate });
      });

      // 按周数排序
      weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

      // 填充排序后的数据
      weekData.forEach((item) => {
        xAxisData.push(item.week + "周");
        linedata.push(item.rate);
      });
    } else {
      // 其他类型（如年度）保持原样
      data.forEach((e) => {
        let formattedLabel = e.label;
        let rate = parseFloat((e.rate * 100).toFixed(12));
        xAxisData.push(formattedLabel);
        linedata.push(rate);
      });
    }

    return { linedata, xAxisData };
  }
};

/**
 * API调用服务
 */
const ApiService = {
  /**
   * 获取汇总数据
   * @param {number} analysisType - 分析类型
   * @param {Object} params - 请求参数
   * @returns {Promise} API响应
   */
  async getSummary(analysisType, params) {
    const endpoint = config.API_ENDPOINTS[analysisType].summary;
    return request.post({
      url: endpoint,
      data: params,
      load: false,
    });
  },

  /**
   * 获取服务报告数据
   * @param {number} analysisType - 分析类型
   * @param {Object} params - 请求参数
   * @returns {Promise} API响应
   */
  async getServiceReport(analysisType, params) {
    const endpoint = config.API_ENDPOINTS[analysisType].serviceReport;
    const extraParams = config.API_PARAMS.serviceReport[analysisType] || {};
    return request.post({
      url: endpoint,
      data: { ...params, ...extraParams },
      load: false,
    });
  },

  /**
   * 获取超时报告数据
   * @param {number} analysisType - 分析类型
   * @param {Object} params - 请求参数
   * @param {string} reportType - 报告类型 ('timeout' 或 'timeoutAccepted')
   * @returns {Promise} API响应
   */
  async getServiceOutReport(analysisType, params, reportType = 'timeout') {
    // 检查是否支持超时报告
    if (!config.API_ENDPOINTS[analysisType].serviceOutReport) {
      throw new Error(`分析类型 ${analysisType} 不支持超时报告`);
    }

    const endpoint = config.API_ENDPOINTS[analysisType].serviceOutReport;
    const extraParams = config.API_PARAMS.serviceOutReport[analysisType]?.[reportType] || {};
    return request.post({
      url: endpoint,
      data: { ...params, ...extraParams },
      load: false,
    });
  },

  /**
   * 获取统计数据
   * @param {number} analysisType - 分析类型
   * @param {Object} params - 请求参数
   * @returns {Promise} API响应
   */
  async getStat(analysisType, params) {
    const endpoint = config.API_ENDPOINTS[analysisType].stat;
    return request.post({
      url: endpoint,
      data: params,
      load: false,
    });
  }
};

/**
 * 图表服务
 */
const ChartService = {
  /**
   * 更新饼图
   * @param {Object} pageContext - 页面上下文
   * @param {Array} data - 数据数组
   * @param {string} title - 图表标题
   */
  async updatePieChart(pageContext, data, title) {
    const ensurePieReady = diyecharts.createChartGuard(pageContext, "chartInstancePie");
    await ensurePieReady();
    diyecharts.updatePieChart(pageContext, data, title);
  },

  /**
   * 更新折线图
   * @param {Object} pageContext - 页面上下文
   * @param {Array} linedata - 折线数据
   * @param {Array} xAxisData - X轴数据
   * @param {number} timeType - 时间类型
   * @param {string} title - 图表标题
   */
  async updateLineChart(pageContext, linedata, xAxisData, timeType, title) {
    const ensureLineReady = diyecharts.createChartGuard(pageContext, "chartInstanceLine");
    await ensureLineReady();
    diyecharts.updateLineChart(pageContext, linedata, xAxisData, timeType, title);
  },

  /**
   * 显示图表加载状态
   * @param {Object} pageContext - 页面上下文
   * @param {string} chartType - 图表类型 ('pie' 或 'line')
   */
  showLoading(pageContext, chartType) {
    const instanceName = chartType === 'pie' ? 'chartInstancePie' : 'chartInstanceLine';
    diyecharts.showLoading(pageContext, instanceName);
  }
};

module.exports = {
  DataProcessor,
  ApiService,
  ChartService
};
