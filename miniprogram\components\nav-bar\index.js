Component({
  properties: {
    title: {
      type: String,
      value: ''
    },
    showLeftArrow: {
      type: Boolean,
      value: false
    },
    goUrl: {
      type: String,
      default: ''
    },
  },
  methods: {
    // 这里是一个自定义方法
    onClickLeft(e){
      var url = e.currentTarget.dataset.url
      if(url == ""||url==undefined){
        wx.navigateBack({
          delta: 1
        });
       }else{
          wx.reLaunch({
            url: url,
          })
       }
    },
  }
})