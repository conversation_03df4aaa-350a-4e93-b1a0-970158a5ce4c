// app.js
App({
  onLaunch() {
   // 展示本地存储能力
    // 登录
    const updateManager = wx.getUpdateManager()
    updateManager.onCheckForUpdate(function (res) {})
    updateManager.onUpdateReady(function () {
      wx.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否马上重启小程序？',
        showCancel: false,
        success: function (res) {
          if (res.confirm) {
            updateManager.applyUpdate()
          }
        }
      })
    })
    updateManager.onUpdateFailed(function () {})
  },
  globalData: {
    url: 'http://localhost:5254/api/',
    //url: 'http://crm-data.squirrelboiler.com/api/',
    //url: 'https://crm-data.devotiongroup.com/api/',
    token:'',
    imageUrl: 'https://dszkfile.devotiongroup.com/',
    userInfo:null,
    isnew: true, // 默认设置为 true，表示未登录状态
    level:1,
  }
})
