<!-- <cu-custom bgColor=""  isBack="{{false}}" ><view slot="content">登录</view></cu-custom> -->
<view class="loginpage">
<!-- -->
<view class="backs" ><van-icon name="arrow-left" bind:tap="back"/></view> 
  <view class="form">
    <image src="https://xsssh.devotiongroup.com/admin/content/images/log.png" style="height: 100px;"></image>
     <view class="padding-20">
          <view class="margin-top-40 text-font-14 text-color-textgray text-align-center">欢迎使用</view>
          <view class="margin-top-10 text-font-14 text-color-textgray text-align-center">登录后将为您提供小松鼠服务数据分析</view>
          <view class="margin-top-50 text-font-14 flex-row" >
            <van-checkbox shape="square" icon-size="16px" value="{{agree}}" bind:change="checkboxChange"/>
            <view>
              <text>我已经阅读并同意</text>
            <text bindtap='agree' style="color: blue; text-decoration: underline;">《用户服务协议》</text>
            <text>和</text>
              <text  bindtap='privacypolicy' style="color: blue; text-decoration: underline;">《隐私政策》</text>
            </view>
          </view>
         <view class="margin-top-20">
          <van-button wx:if="{{agree}}"   type="info" round block open-type="getPhoneNumber" bindgetphonenumber="submit">一键登录</van-button>
          <van-button wx:else  disabled type="info" round block open-type="getPhoneNumber" bindgetphonenumber="submit">一键登录</van-button>
         </view>
         <view class="text-font-14  text-color-textgray padding-top-20  text-align-center" bind:tap="back">暂不登录</view> 
     
     </view>
</view>
</view>
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
