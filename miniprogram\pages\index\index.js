// pages/index/index.js
var userInfoAny = require("../../utils/userInfoAny.js");
const app = getApp();
var that;
Page({
  /**
   * 页面的初始数据
   */
  data: {
    imageUrl: app.globalData.imageUrl,
    showPrivacy: false,
    isShow: true,
  },
  createFamily: function () {
    if (wx.getPrivacySetting != undefined) {
      wx.getPrivacySetting({
        success: (res) => {
          if (res.needAuthorization) {
            // 需要弹出隐私协议
            this.setData({
              showPrivacy: true,
            });
          } else {
            // 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用已声明过的隐私接口
            wx.navigateTo({
              url: "/pages/login/login", //跳转
            });
          }
        },
        fail: () => {},
        complete: () => {},
      });
    } else {
      wx.navigateTo({
        url: "/pages/login/login", //跳转
      });
    }
  },
  colsed: function () {
    this.setData({
      showPrivacy: false,
    });
  },
  onLoad: function (options) {
    that = this;
    if (wx.getPrivacySetting != undefined) {
      wx.getPrivacySetting({
        success: (res) => {
          if (res.needAuthorization) {
            // 需要弹出隐私协议
            that.setData({
              showPrivacy: true,
            });
          } else {
            // 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用已声明过的隐私接口
            // wx.showLoading({
            //   title: '加载中',
            // })
            that.UserIsAny();
          }
        },
        fail: () => {},
        complete: () => {},
      });
    } else {
      that.UserIsAny();
      wx.hideLoading();
    }
  },
  handleAgreePrivacyAuthorization() {
    // 用户同意隐私协议事件回调
    that.setData({
      showPrivacy: false,
    });
    that.UserIsAny();
  },
  handleOpenPrivacyContract() {
    // 打开隐私协议页面
    wx.openPrivacyContract({
      success: () => {}, // 打开成功
      fail: () => {}, // 打开失败
      complete: () => {},
    });
  },
  UserIsAny: function () {
    userInfoAny.getUserInfoAny(
      (res) => {
        if (res.data.id != 0) {
          app.globalData.isnew = false;
        } else {
          app.globalData.isnew = true;

          switch (res.data.roleId) {
            case 4:
               app.globalData.level = 5;
               app.globalData.level = 5;
              break;
            case 6:
               app.globalData.level = 3;
              break;
            case 7:
              app.globalData.level = 4;
              break;
             default:
              app.globalData.level = 1;
         

          }
        }

       wx.reLaunch({
          url: "/pages/dataOverview/index",
        });
   
        // wx.reLaunch({
        //   url: "/pages/workorderAnalysis/index",
        // });
      },
      (err) => {
        if (err == "手机未绑定，前往绑定") {
          wx.reLaunch({
            url: "/pages/login/login",
          });
        }
        if (err == "账号未启用,请联系管理员") {
          // wx.reLaunch({
          //   url: "/pages/isEnable/index",
          // });
        }
      }
    );
  },
});
