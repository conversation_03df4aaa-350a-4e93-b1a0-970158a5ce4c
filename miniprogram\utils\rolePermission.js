/**
 * 角色权限控制工具
 * 用于统一管理不同角色的权限和level参数
 */

/**
 * 角色ID常量定义
 */
const ROLE_IDS = {
  STORE_MANAGER: 6,    // 门店管理员
  ENGINEER: 7          // 工程师
};

/**
 * Level参数映射
 */
const LEVEL_MAPPING = {
  REGION: 1,      // 区域
  CITY: 2,        // 城市
  STORE: 3,       // 门店
  ENGINEER: 4,    // 工程师
  DISPATCHER: 5   // 调度员
};

/**
 * 根据用户角色获取对应的level参数
 * @param {Object} userInfo - 用户信息对象
 * @param {number} defaultLevel - 默认level值（通常是选中的tab索引+1）
 * @returns {number} - 对应的level参数
 */
const getLevelByRole = (userInfo, defaultLevel = 1) => {
  if (!userInfo || !userInfo.roleId) {
    return defaultLevel;
  }

  switch (userInfo.roleId) {
    case ROLE_IDS.STORE_MANAGER:
      return LEVEL_MAPPING.STORE;     // 门店管理员固定为门店级别
    case ROLE_IDS.ENGINEER:
      return LEVEL_MAPPING.ENGINEER;  // 工程师固定为工程师级别
    default:
      return defaultLevel;            // 其他角色使用默认值
  }
};

/**
 * 检查用户是否需要隐藏tab切换
 * @param {Object} userInfo - 用户信息对象
 * @returns {boolean} - true表示需要隐藏tab，false表示显示tab
 */
const shouldHideTabs = (userInfo) => {
  if (!userInfo || !userInfo.roleId) {
    return false;
  }

  return userInfo.roleId === ROLE_IDS.STORE_MANAGER ||
         userInfo.roleId === ROLE_IDS.ENGINEER;
};

/**
 * 获取角色对应的显示名称
 * @param {Object} userInfo - 用户信息对象
 * @returns {string} - 角色显示名称
 */
const getRoleDisplayName = (userInfo) => {
  if (!userInfo || !userInfo.roleId) {
    return '';
  }

  switch (userInfo.roleId) {
    case ROLE_IDS.STORE_MANAGER:
      return '门店';
    case ROLE_IDS.ENGINEER:
      return '工程师';
    default:
      return '';
  }
};

/**
 * 获取level对应的显示名称
 * @param {number} level - level参数
 * @returns {string} - level显示名称
 */
const getLevelDisplayName = (level) => {
  switch (level) {
    case LEVEL_MAPPING.REGION:
      return '区域';
    case LEVEL_MAPPING.CITY:
      return '城市';
    case LEVEL_MAPPING.STORE:
      return '门店';
    case LEVEL_MAPPING.ENGINEER:
      return '工程师';
    case LEVEL_MAPPING.DISPATCHER:
      return '调度员';
    default:
      return '';
  }
};

/**
 * 检查用户是否有访问特定level的权限
 * @param {Object} userInfo - 用户信息对象
 * @param {number} targetLevel - 目标level
 * @returns {boolean} - true表示有权限，false表示无权限
 */
const hasLevelPermission = (userInfo, targetLevel) => {
  if (!userInfo || !userInfo.roleId) {
    return false;
  }

  const userLevel = getLevelByRole(userInfo, 1);

  // 用户只能访问自己级别及以下的数据
  return targetLevel >= userLevel;
};

/**
 * 获取用户可访问的level列表
 * @param {Object} userInfo - 用户信息对象
 * @returns {Array} - 可访问的level数组
 */
const getAccessibleLevels = (userInfo) => {
  if (!userInfo || !userInfo.roleId) {
    return [1, 2, 3, 4, 5]; // 默认全部可访问
  }

  const userLevel = getLevelByRole(userInfo, 1);
  const levels = [];

  for (let i = userLevel; i <= 5; i++) {
    levels.push(i);
  }

  return levels;
};

/**
 * 根据用户角色获取对应的value参数
 * @param {Object} userInfo - 用户信息对象
 * @param {*} defaultValue - 默认value值（通常是选中的区域值）
 * @returns {*} - 对应的value参数
 */
const getValueByRole = (userInfo, defaultValue = null) => {
  if (!userInfo || !userInfo.roleId) {
    return defaultValue;
  }

  switch (userInfo.roleId) {
    case ROLE_IDS.STORE_MANAGER:
      return userInfo.servicePointId || 0; // 门店管理员使用servicePointId，为null时使用0
    case ROLE_IDS.ENGINEER:
      return userInfo.engineerId || 0;     // 工程师使用engineerId，为null时使用0
    default:
      return defaultValue;                 // 其他角色使用默认值
  }
};

module.exports = {
  ROLE_IDS,
  LEVEL_MAPPING,
  getLevelByRole,
  shouldHideTabs,
  getRoleDisplayName,
  getLevelDisplayName,
  hasLevelPermission,
  getAccessibleLevels,
  getValueByRole
};
