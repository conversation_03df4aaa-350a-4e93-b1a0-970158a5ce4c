import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
 //Toast
 function Toasts(str,callback){
  Toast({
    type:'text',
    message:str,
    onClose: () => {
      callback();
    },
  });
 }
 function TSuccess(str,callback){
   Toast({
    type:'success',
    message:str,
    onClose: () => {
      callback();
    },
  });
 }
 function Tfail(str,callback){
  Toast({
    type:'fail',
    message:str,
    onClose: () => {
      callback();
    },
  });
}
function Tclear(){
  wx.hideLoading()
}
function Tloading(message="加载中..."){
  wx.showLoading({
    title:message,
    mask:true
  })
  // Toast.loading({
  //   message: message,
  //   duration:0,
  //   forbidClick: true,
  //   loadingType: 'spinner',
  // });
}
function Dialogs(message,callback){
  Dialog.alert({
    message: message,
    zIndex:999
  }).then(() => {
    callback()
  });
}
function DialogAlert(message,callback,title="温馨提示"){
  Dialog.alert({
    title: title,
    message: message,
    zIndex:999,
  }).then(() => {
     callback()
  });
}
function DialogConfirm(message,callback,title="温馨提示"){
  Dialog.confirm({
    title: title,
    message: message,
    zIndex:999,
  }).then(() => {
     callback()
  }) 
  .catch(() => {

  });
}
function DialogConfirmCancel(message,callback,cancel,title="温馨提示"){
  Dialog.confirm({
    title: title,
    message: message,

  }).then(() => {
     callback()
  }) 
  .catch(() => {
    cancel()
  });
}

module.exports = {
  Toasts,TSuccess,Tfail,Tclear,Tloading,Dialogs,DialogAlert,DialogConfirm,DialogConfirmCancel
}