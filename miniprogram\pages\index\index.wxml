<!--pages/index/index.wxml-->
<view class="main-page-view">
  <view class="addBox" wx:if="{{isShow}}">
    <van-loading type="spinner" color="#1989fa" >加载中</van-loading>
  </view>
  <view>
    <van-popup show="{{showPrivacy}}" round custom-style="width: 80%">
      <view class="padding-10">
        <view class="padding-top-10 text-font-bold text-align-center text-font-18">用户隐私保护提示</view>
        <view class="padding-10">
          <p>
            <span>
              感谢您使用本小程序，在使用前您应当阅读并同意
            </span>
            <span style="color:blue;" bindtap="handleOpenPrivacyContract">《用户隐私保护指引》
            </span>
            <span>，当点击同意并继续时，即表示您已理解并同意该条款内容，该条款将对您产生法律约束力;如您不同意，将无法继续使用该小程序相关功能。
            </span>
          </p>
        </view>
        <view class="padding-lr-30 padding-tb-10">
          <van-row gutter="20">
            <van-col span="12">
              <van-button type="default" block round color="#EFEFEF" custom-class="text-color-black" bind:click="colsed">不同意</van-button>
            </van-col>
            <van-col span="12">
              <van-button type="info" block round open-type="agreePrivacyAuthorization" bind:click="handleAgreePrivacyAuthorization">同意并继续</van-button>
            </van-col>
          </van-row>
        </view>
      </view>
    </van-popup>
  </view>
</view>
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />