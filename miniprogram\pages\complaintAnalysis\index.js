var dateUtil = require("../../utils/dateUtil.js");
const request = require("../../utils/request.js");
var diyecharts = require("../../utils/diyecharts.js");
const rolePermission = require("../../utils/rolePermission.js");
const app = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    level:0,
    isnew: false, //判断是否新用户（未登录）
    timeshow: false,
    minDate: new Date(2015, 0, 1).getTime(),
    maxDate: new Date(2035, 11, 1).getTime(),
    currentDate: "",
    startTime: dateUtil.getLast30DaysStartDate(),
    endTime: dateUtil.getLast30DaysEndDate(),
    selectedYear: new Date().getFullYear(),
    active: 1, // 默认选中"年度"选项卡
    sractive1:0,
    sractive2:0,
    tType: 1,
    timeType: 1,
    title1: ["工单汇总", "投诉汇总", "投诉大类汇总", "真实投诉原因大类汇总"],
    title2: ["72小时完成率", "投诉率", "投诉分类占比", "真实投诉原因占比"],
    title3: ["投诉完成率", "", "", ""],
    value: 0,
    option: [
      { text: "用户投诉完成率分析", value: 0 },
      { text: "投诉率分析", value: 1 },
      { text: "投诉分类分析", value: 2 },
      { text: "真实投诉原因分类分析", value: 3 },
    ],
    yearColumns: [
      {
        values: [
          "2020",
          "2021",
          "2022",
          "2023",
          "2024",
          "2025",
          "2026",
          "2027",
          "2028",
          "2029",
          "2030",
        ],
      },
    ],
    yearIndex: 0, // 初始值，将在onLoad中根据selectedYear计算
    yearshow: false,
    regioninfo: [],
    selectedRegion: "暂无",
    selectedRegionValue: "",
    regionIndex: 0,
    regionshow: false,
    pieec: {
      onInit: null,
    },

    lineec: {
      onInit: null,
    },
    barec: {
      onInit: null,
    },
    summaryinfo: [],
    serviceReportinfo: [],
    serviceReportinfoAll: [], // 存储所有数据
    serviceReportinfoDisplayed: [], // 当前显示的数据
    showMoreButton: false, // 是否显示"查看更多"按钮
    showCollapseButton: false, // 是否显示"收起"按钮
    isExpanded: false, // 是否已展开
    serviceReportinfo1: [],
    serviceReportinfo1All: [], // 存储所有数据
    serviceReportinfo1Displayed: [], // 当前显示的数据
    showMoreButton1: false, // 是否显示"查看更多"按钮
    showCollapseButton1: false, // 是否显示"收起"按钮
    isExpanded1: false, // 是否已展开
    pageSize: 10, // 每页显示的数量
    serviceReportinfoloading: true,
    serviceReportinfo1loading: true,
  },

  //用户投诉完成率分析
  ComplaintResolutionRateGetSummary() {
    const ensurePieReady = diyecharts.createChartGuard(
      this,
      "chartInstancePie"
    );
    diyecharts.showLoading(this, "chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };

    request
      .post({
        url: "ComplaintResolutionRate/GetSummary",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensurePieReady(); // 等待图表实例创建
        this.setData({
          summaryinfo: res.data,
        });
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(
          this,
          piedata,
          this.data.title1[this.data.value]
        );
      });
  },
  //72小时完成率
  ComplaintResolutionRateGetServiceReport() {
    this.setData({
      serviceReportinfoloading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive1 + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      type: 1,
    };
    request
      .post({
        url: "ComplaintResolutionRate/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data
          .filter((item) => item.label !== null) // 过滤掉label为null的项
        .map((item) => {

            // 确保数据是数值类型
            // 计算百分比（用于进度条，需要是数值）
            const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
            // 返回处理后的数据，添加格式化后的百分比
            return {
              ...item,
              // 数值形式的百分比，用于进度条
              ratePercent: ratePercentValue,
            };

        });
       console.log(processedData)
        // 存储所有数据
        this.setData({
          serviceReportinfoloading: false,
          serviceReportinfoAll: processedData,
        });

        // 处理分页显示
        this.updateDisplayedData();
      })
      .catch((err) => {
        console.error("获取72小时完成率数据失败:", err);
        this.setData({
          serviceReportinfo: [],
          serviceReportinfoAll: [],
          serviceReportinfoDisplayed: [],
          showMoreButton: false,
          showCollapseButton: false,
          isExpanded: false,
          serviceReportinfoloading: false
        });
      });
  },
  //投诉完成率
  ComplaintResolutionRateGetServiceReport1() {
    this.setData({
      serviceReportinfo1loading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive2 + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
    };
    request
      .post({
        url: "ComplaintResolutionRate/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data
          .filter((item) => item.label !== null) // 过滤掉label为null的项
        .map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
          };
        });

        // 存储所有数据
        this.setData({
          serviceReportinfo1loading: false,
          serviceReportinfo1All: processedData,
        });

        // 处理分页显示
        this.updateDisplayedData1();
      })
      .catch((err) => {
        console.error("获取投诉完成率数据失败:", err);
        this.setData({
          serviceReportinfo1: [],
          serviceReportinfo1All: [],
          serviceReportinfo1Displayed: [],
          showMoreButton1: false,
          showCollapseButton1: false,
          isExpanded1: false,
          serviceReportinfo1loading: false
        });
      });
  },
  ComplaintResolutionRateGetStat() {
    const ensureLineReady = diyecharts.createChartGuard(
      this,
      "chartInstanceLine"
    );
    diyecharts.showLoading(this, "chartInstanceLine");
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;

    // 使用全局权限工具获取value参数
    const value = rolePermission.getValueByRole(this.data.userInfo, this.data.selectedRegionValue);

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        value: value
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level: this.data.level,
        timeType: timeType,
        value: value
      };
    }
    request
      .post({
        url: "ComplaintResolutionRate/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建
        const linedata = [];
        const xAxisData = [];

        // 根据不同的 timeType 格式化标签
        if (timeType == 2) {
          // 自然月
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({ length: 12 }, (_, i) =>
            (i + 1).toString()
          );
          const monthData = {};

          // 初始化所有月份的数据为0
          allMonths.forEach((month) => {
            monthData[month] = 0;
          });

          // 填充已有的月份数据
          res.data.forEach((e) => {
            // 提取月份数字
            let month = e.label;
            if (month.includes("-")) {
              // 如果是"2025-11"这样的格式，提取月份部分
              month = month.split("-")[1];
            }
            // 去掉前导零
            month = parseInt(month).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            monthData[month] = rate;
          });

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 按月份顺序填充数据
          allMonths.forEach((month) => {
            xAxisData.push(month + "月");
            linedata.push(monthData[month]);
          });
        } else if (timeType == 3) {
          // 自然周
          // 对于自然周，也可以实现类似的排序和补齐逻辑
          // 这里先简单处理，只进行排序
          const weekData = [];

          res.data.forEach((e) => {
            let week = e.label;
            if (week.includes("-")) {
              // 如果是"2025-11"这样的格式，提取周数部分
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            weekData.push({
              week: week,
              rate: rate,
            });
          });

          // 按周数排序
          weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 填充排序后的数据
          weekData.forEach((item) => {
            xAxisData.push(item.week + "周");
            linedata.push(item.rate);
          });
        } else {
          // 其他类型（如年度）保持原样
          res.data.forEach((e) => {
            let formattedLabel = e.label;
            let rate = parseFloat((e.rate * 100).toFixed(12));
            xAxisData.push(formattedLabel);
            linedata.push(rate);
          });
        }

        diyecharts.updateLineChart(
          this,
          linedata,
          xAxisData,
          timeType,
          "投诉完成率"
        );
      });
  },

  //投诉率
  ComplaintRateGetSummary() {
    const ensurePieReady = diyecharts.createChartGuard(
      this,
      "chartInstancePie"
    );
    diyecharts.showLoading(this, "chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };

    request
      .post({
        url: "ComplaintRate/GetSummary",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensurePieReady(); // 等待图表实例创建
        this.setData({
          summaryinfo: res.data,
        });
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(
          this,
          piedata,
          this.data.title1[this.data.value]
        );
      });
  },
  ComplaintRateGetServiceReport() {
    this.setData({
      serviceReportinfoloading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive1 + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level
    };
    request
      .post({
        url: "ComplaintRate/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data
          .filter((item) => item.label !== null) // 过滤掉label为null的项
        .map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
          };
        });

        // 存储所有数据
        this.setData({
          serviceReportinfoloading: false,
          serviceReportinfoAll: processedData,
        });

        // 处理分页显示
        this.updateDisplayedData();
      })
      .catch((err) => {
        console.error("获取投诉率数据失败:", err);
        this.setData({
          serviceReportinfo: [],
          serviceReportinfoAll: [],
          serviceReportinfoDisplayed: [],
          showMoreButton: false,
          showCollapseButton: false,
          isExpanded: false,
          serviceReportinfoloading: false
        });
      });
  },
  ComplaintRateGetStat() {
    const ensureLineReady = diyecharts.createChartGuard(
      this,
      "chartInstanceLine"
    );
    diyecharts.showLoading(this, "chartInstanceLine");
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;

    // 使用全局权限工具获取value参数
    const value = rolePermission.getValueByRole(this.data.userInfo, this.data.selectedRegionValue);

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        value: value
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level: this.data.level,
        timeType: timeType,
        value: value
      };
    }
    request
      .post({
        url: "ComplaintRate/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建
        const linedata = [];
        const xAxisData = [];

        // 根据不同的 timeType 格式化标签
        if (timeType == 2) {
          // 自然月
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({ length: 12 }, (_, i) =>
            (i + 1).toString()
          );
          const monthData = {};

          // 初始化所有月份的数据为0
          allMonths.forEach((month) => {
            monthData[month] = 0;
          });

          // 填充已有的月份数据
          res.data.forEach((e) => {
            // 提取月份数字
            let month = e.label;
            if (month.includes("-")) {
              // 如果是"2025-11"这样的格式，提取月份部分
              month = month.split("-")[1];
            }
            // 去掉前导零
            month = parseInt(month).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            monthData[month] = rate;
          });

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 按月份顺序填充数据
          allMonths.forEach((month) => {
            xAxisData.push(month + "月");
            linedata.push(monthData[month]);
          });
        } else if (timeType == 3) {
          // 自然周
          // 对于自然周，也可以实现类似的排序和补齐逻辑
          // 这里先简单处理，只进行排序
          const weekData = [];

          res.data.forEach((e) => {
            let week = e.label;
            if (week.includes("-")) {
              // 如果是"2025-11"这样的格式，提取周数部分
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            weekData.push({
              week: week,
              rate: rate,
            });
          });

          // 按周数排序
          weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 填充排序后的数据
          weekData.forEach((item) => {
            xAxisData.push(item.week + "周");
            linedata.push(item.rate);
          });
        } else {
          // 其他类型（如年度）保持原样
          res.data.forEach((e) => {
            let formattedLabel = e.label;
            let rate = parseFloat((e.rate * 100).toFixed(12));
            xAxisData.push(formattedLabel);
            linedata.push(rate);
          });
        }

        diyecharts.updateLineChart(
          this,
          linedata,
          xAxisData,
          timeType,
          "投诉率"
        );
      });
  },

  //投诉分类分析
  ComplaintCategoriesGetSummary() {
    const ensurePieReady = diyecharts.createChartGuard(
      this,
      "chartInstancePie"
    );
    diyecharts.showLoading(this, "chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };

    request
      .post({
        url: "ComplaintCategories/GetSummary",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensurePieReady(); // 等待图表实例创建
        this.setData({
          summaryinfo: res.data,
        });
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(
          this,
          piedata,
          this.data.title1[this.data.value]
        );
      })
      .catch((error) => {
        console.error("获取投诉分类汇总数据失败:", error);
        // 显示错误信息
        if (this.chartInstancePie) {
          this.chartInstancePie.hideLoading();
          this.chartInstancePie.setOption(
            {
              graphic: {
                type: "text",
                left: "center",
                top: "middle",
                style: {
                  text: "加载数据失败",
                  fill: "#999",
                  fontSize: 16,
                },
              },
            },
            {
              replaceMerge: ["graphic"],
            }
          );
        }
      });
  },
  ComplaintCategoriesGetServiceReport() {
    const ensureBarReady = diyecharts.createChartGuard(
      this,
      "chartInstanceStackedBar"
    );
    diyecharts.showLoading(this, "chartInstanceStackedBar");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: this.data.level,
      type: 1,
    };
    request
      .post({
        url: "ComplaintCategories/GetServiceReport",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureBarReady(); // 等待图表实例创建

        // 处理多组数据
        if (res.data && res.data.length > 0) {
          // 1. 提取所有分组标签（如区域名称）
          const groupLabels = res.data.map((group) => group.label || "null");

          // 2. 提取所有投诉分类
          const allCategories = new Set();
          res.data.forEach((group) => {
            if (group.children && group.children.length > 0) {
              group.children.forEach((category) => {
                allCategories.add(category.label);
              });
            }
          });
          const categories = Array.from(allCategories);

          // 3. 为每个投诉分类创建一个数据系列
          const seriesData = [];
          categories.forEach((category) => {
            const data = [];
            // 遍历每个分组
            res.data.forEach((group) => {
              // 查找该分组中的对应分类
              const categoryData = group.children
                ? group.children.find((item) => item.label === category)
                : null;

              // 如果找到了数据，添加到系列中，否则添加0
              if (categoryData && typeof categoryData.value === "number") {
                data.push(categoryData.value);
              } else {
                data.push(0);
              }
            });
            seriesData.push(data);
          });

          // 调试输出
          console.log("横向堆叠柱状图数据:", {
            categories,
            groupLabels,
            seriesData,
            hasData:
              seriesData.length > 0 &&
              seriesData.some((series) => series.some((value) => value > 0)),
          });

          // 4. 更新图表
          if (seriesData.length > 0 && groupLabels.length > 0) {
            diyecharts.updateHorizontalStackedBar(
              this,
              seriesData,
              groupLabels,
              categories
            );
          } else {
            console.warn("没有有效的数据用于更新横向堆叠柱状图");
            this.chartInstanceStackedBar.hideLoading();
            this.chartInstanceStackedBar.setOption(
              {
                graphic: {
                  type: "text",
                  left: "center",
                  top: "middle",
                  style: {
                    text: "暂无数据",
                    fill: "#999",
                    fontSize: 16,
                  },
                },
              },
              { replaceMerge: ["graphic"] }
            );
          }
        } else {
          // 无数据处理
          this.chartInstanceStackedBar.hideLoading();
          this.chartInstanceStackedBar.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "暂无数据",
                fill: "#999",
                fontSize: 16,
              },
            },
          });
        }
      })
      .catch((error) => {
        console.error("获取投诉分类服务报告失败:", error);
        // 显示错误信息
        if (this.chartInstanceStackedBar) {
          this.chartInstanceStackedBar.hideLoading();
          this.chartInstanceStackedBar.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "加载数据失败",
                fill: "#999",
                fontSize: 16,
              },
            },
          });
        }
      });
  },
  ComplaintCategoriesGetStat() {
    const ensureLineReady = diyecharts.createChartGuard(
      this,
      "chartInstanceLine"
    );
    diyecharts.showLoading(this, "chartInstanceLine");
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        // value:this.data.selectedRegionValue
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level: this.data.level,
        timeType: timeType,
        // value:this.data.selectedRegionValue
      };
    }
    request
      .post({
        url: "ComplaintCategories/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建

        // 处理多组数据
        if (timeType == 1) {
          // 年度数据 - 多系列处理
          // 1. 提取所有年份作为 X 轴数据
          const xAxisData = res.data.map((item) => item.label);

          // 2. 提取所有投诉分类
          const allCategories = new Set();
          res.data.forEach((yearData) => {
            if (yearData.children && yearData.children.length > 0) {
              yearData.children.forEach((category) => {
                allCategories.add(category.label);
              });
            }
          });
          const categories = Array.from(allCategories);

          // 3. 为每个分类创建一个数据系列
          const seriesData = [];
          categories.forEach((category) => {
            const data = [];
            // 遍历每一年
            res.data.forEach((yearData) => {
              // 查找该年份中的对应分类
              const categoryData = yearData.children
                ? yearData.children.find((item) => item.label === category)
                : null;

              // 如果找到了数据，添加到系列中，否则添加0
              if (categoryData) {
                data.push(categoryData.value);
              } else {
                data.push(0);
              }
            });
            seriesData.push(data);
          });

          // 4. 更新图表
          diyecharts.updateLineChart(
            this,
            seriesData,
            xAxisData,
            timeType,
            categories
          );
        } else if (timeType == 2) {
          // 自然月数据处理
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({ length: 12 }, (_, i) =>
            (i + 1).toString()
          );

          // 提取所有投诉分类
          const allCategories = new Set();
          res.data.forEach((monthData) => {
            if (monthData.children && monthData.children.length > 0) {
              monthData.children.forEach((category) => {
                allCategories.add(category.label);
              });
            }
          });
          const categories = Array.from(allCategories);

          // 为每个分类创建一个数据系列
          const seriesData = [];
          categories.forEach((category) => {
            const monthValues = {};

            // 初始化所有月份的数据为0
            allMonths.forEach((month) => {
              monthValues[month] = 0;
            });

            // 填充已有的月份数据
            res.data.forEach((monthData) => {
              // 提取月份数字
              let month = monthData.label;
              if (month.includes("-")) {
                month = month.split("-")[1];
              }
              // 去掉前导零
              month = parseInt(month).toString();

              // 查找该月份中的对应分类
              const categoryData = monthData.children
                ? monthData.children.find((item) => item.label === category)
                : null;

              if (categoryData) {
                monthValues[month] = categoryData.value;
              }
            });

            // 按月份顺序填充数据
            const data = allMonths.map((month) => monthValues[month]);
            seriesData.push(data);
          });

          // 格式化X轴标签
          const xAxisData = allMonths.map((month) => month + "月");

          // 更新图表
          diyecharts.updateLineChart(
            this,
            seriesData,
            xAxisData,
            timeType,
            categories
          );
        } else if (timeType == 3) {
          // 自然周数据处理
          // 提取所有周数并排序
          const allWeeks = new Set();
          res.data.forEach((weekData) => {
            let week = weekData.label;
            if (week.includes("-")) {
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();
            allWeeks.add(week);
          });
          const sortedWeeks = Array.from(allWeeks).sort(
            (a, b) => parseInt(a) - parseInt(b)
          );

          // 提取所有投诉分类
          const allCategories = new Set();
          res.data.forEach((weekData) => {
            if (weekData.children && weekData.children.length > 0) {
              weekData.children.forEach((category) => {
                allCategories.add(category.label);
              });
            }
          });
          const categories = Array.from(allCategories);

          // 为每个分类创建一个数据系列
          const seriesData = [];
          categories.forEach((category) => {
            const weekValues = {};

            // 初始化所有周的数据为0
            sortedWeeks.forEach((week) => {
              weekValues[week] = 0;
            });

            // 填充已有的周数据
            res.data.forEach((weekData) => {
              // 提取周数
              let week = weekData.label;
              if (week.includes("-")) {
                week = week.split("-")[1];
              }
              // 去掉前导零
              week = parseInt(week).toString();

              // 查找该周中的对应分类
              const categoryData = weekData.children
                ? weekData.children.find((item) => item.label === category)
                : null;

              if (categoryData) {
                weekValues[week] = categoryData.value;
              }
            });

            // 按周顺序填充数据
            const data = sortedWeeks.map((week) => weekValues[week]);
            seriesData.push(data);
          });

          // 格式化X轴标签
          const xAxisData = sortedWeeks.map((week) => week + "周");

          // 更新图表
          diyecharts.updateLineChart(
            this,
            seriesData,
            xAxisData,
            timeType,
            categories
          );
        } else {
          // 其他情况，显示无数据
          diyecharts.updateLineChart(this, [], [], timeType, ["暂无数据"]);
        }
      })
      .catch((error) => {
        console.error("获取投诉分类数据失败:", error);
        // 显示错误信息
        if (this.chartInstanceLine) {
          this.chartInstanceLine.hideLoading();
          this.chartInstanceLine.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "加载数据失败",
                fill: "#999",
                fontSize: 16,
              },
            },
          });
        }
      });
  },

  //真实投诉原因分类分析
  RootCauseAnalysisGetSummary() {
    const ensurePieReady = diyecharts.createChartGuard(
      this,
      "chartInstancePie"
    );
    diyecharts.showLoading(this, "chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };

    request
      .post({
        url: "RootCauseAnalysis/GetSummary",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensurePieReady(); // 等待图表实例创建
        this.setData({
          summaryinfo: res.data,
        });
        const piedata = res.data

          .filter((e) => e.pieShowFlag&&e.title !== null) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(
          this,
          piedata,
          this.data.title1[this.data.value]
        );
      })
      .catch((error) => {
        // 显示错误信息
        if (this.chartInstancePie) {
          this.chartInstancePie.hideLoading();
          this.chartInstancePie.setOption(
            {
              graphic: {
                type: "text",
                left: "center",
                top: "middle",
                style: {
                  text: "加载数据失败",
                  fill: "#999",
                  fontSize: 16,
                },
              },
            },
            {
              replaceMerge: ["graphic"],
            }
          );
        }
      });
  },

  RootCauseAnalysisGetUserRootCause() {
    const ensurePieReady = diyecharts.createChartGuard(
      this,
      "chartInstancePie"
    );
    diyecharts.showLoading(this, "chartInstancePie");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };

    request
      .post({
        url: "RootCauseAnalysis/GetUserRootCause",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensurePieReady(); // 等待图表实例创建
        this.setData({
          summaryinfo: res.data,
        });
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(
          this,
          piedata,
          this.data.title2[this.data.value]
        );
      })
      .catch((error) => {
        // 显示错误信息
        if (this.chartInstancePie) {
          this.chartInstancePie.hideLoading();
          this.chartInstancePie.setOption(
            {
              graphic: {
                type: "text",
                left: "center",
                top: "middle",
                style: {
                  text: "加载数据失败",
                  fill: "#999",
                  fontSize: 16,
                },
              },
            },
            {
              replaceMerge: ["graphic"],
            }
          );
        }
      });
  },
  RootCauseAnalysisGetServiceReport() {
    const ensureBarReady = diyecharts.createChartGuard(
      this,
      "chartInstanceStackedBar"
    );
    diyecharts.showLoading(this, "chartInstanceStackedBar");
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: this.data.level,
      type: 1,
    };
    request
      .post({
        url: "RootCauseAnalysis/GetServiceReport",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureBarReady(); // 等待图表实例创建

        // 处理多组数据
        if (res.data && res.data.length > 0) {
          // 1. 提取所有分组标签（如区域名称）
          const groupLabels = res.data
          .filter((item) => item.label !== null) // 过滤掉label为null的项
          .map((group) => group.label || "null");

          // 2. 提取所有投诉分类
          const allCategories = new Set();
          res.data.forEach((group) => {
            if (group.children && group.children.length > 0) {
              group.children.forEach((category) => {
                allCategories.add(category.label);
              });
            }
          });
          const categories = Array.from(allCategories);

          // 3. 为每个投诉分类创建一个数据系列
          const seriesData = [];
          categories.forEach((category) => {
            const data = [];
            // 遍历每个分组
            res.data.forEach((group) => {
              // 查找该分组中的对应分类
              const categoryData = group.children
                ? group.children.find((item) => item.label === category)
                : null;

              // 如果找到了数据，添加到系列中，否则添加0
              if (categoryData && typeof categoryData.value === "number") {
                data.push(categoryData.value);
              } else {
                data.push(0);
              }
            });
            seriesData.push(data);
          });

          // 调试输出
          console.log("横向堆叠柱状图数据:", {
            categories,
            groupLabels,
            seriesData,
            hasData:
              seriesData.length > 0 &&
              seriesData.some((series) => series.some((value) => value > 0)),
          });

          // 4. 更新图表
          if (seriesData.length > 0 && groupLabels.length > 0) {
            diyecharts.updateHorizontalStackedBar(
              this,
              seriesData,
              groupLabels,
              categories
            );
          } else {
            console.warn("没有有效的数据用于更新横向堆叠柱状图");
            this.chartInstanceStackedBar.hideLoading();
            this.chartInstanceStackedBar.setOption(
              {
                graphic: {
                  type: "text",
                  left: "center",
                  top: "middle",
                  style: {
                    text: "暂无数据",
                    fill: "#999",
                    fontSize: 16,
                  },
                },
              },
              { replaceMerge: ["graphic"] }
            );
          }
        } else {
          // 无数据处理
          this.chartInstanceStackedBar.hideLoading();
          this.chartInstanceStackedBar.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "暂无数据",
                fill: "#999",
                fontSize: 16,
              },
            },
          });
        }
      })
      .catch((error) => {
        console.error("获取投诉分类服务报告失败:", error);
        // 显示错误信息
        if (this.chartInstanceStackedBar) {
          this.chartInstanceStackedBar.hideLoading();
          this.chartInstanceStackedBar.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "加载数据失败",
                fill: "#999",
                fontSize: 16,
              },
            },
          });
        }
      });
  },
  RootCauseAnalysisGetStat() {
    const ensureLineReady = diyecharts.createChartGuard(
      this,
      "chartInstanceLine"
    );
    diyecharts.showLoading(this, "chartInstanceLine");
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        // value:this.data.selectedRegionValue
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level: this.data.level,
        timeType: timeType,
        // value:this.data.selectedRegionValue
      };
    }
    request
      .post({
        url: "RootCauseAnalysis/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建

        // 处理多组数据
        if (timeType == 1) {
          // 年度数据 - 多系列处理
          // 1. 提取所有年份作为 X 轴数据
          const xAxisData = res.data.map((item) => item.label);

          // 2. 提取所有投诉分类
          const allCategories = new Set();
          res.data.forEach((yearData) => {
            if (yearData.children && yearData.children.length > 0) {
              yearData.children.forEach((category) => {
                allCategories.add(category.label);
              });
            }
          });
          const categories = Array.from(allCategories);

          // 3. 为每个分类创建一个数据系列
          const seriesData = [];
          categories.forEach((category) => {
            const data = [];
            // 遍历每一年
            res.data.forEach((yearData) => {
              // 查找该年份中的对应分类
              const categoryData = yearData.children
                ? yearData.children.find((item) => item.label === category)
                : null;

              // 如果找到了数据，添加到系列中，否则添加0
              if (categoryData) {
                data.push(categoryData.value);
              } else {
                data.push(0);
              }
            });
            seriesData.push(data);
          });

          // 4. 更新图表
          diyecharts.updateLineChart(
            this,
            seriesData,
            xAxisData,
            timeType,
            categories
          );
        } else if (timeType == 2) {
          // 自然月数据处理
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({ length: 12 }, (_, i) =>
            (i + 1).toString()
          );

          // 提取所有投诉分类
          const allCategories = new Set();
          res.data.forEach((monthData) => {
            if (monthData.children && monthData.children.length > 0) {
              monthData.children.forEach((category) => {
                allCategories.add(category.label);
              });
            }
          });
          const categories = Array.from(allCategories);

          // 为每个分类创建一个数据系列
          const seriesData = [];
          categories.forEach((category) => {
            const monthValues = {};

            // 初始化所有月份的数据为0
            allMonths.forEach((month) => {
              monthValues[month] = 0;
            });

            // 填充已有的月份数据
            res.data.forEach((monthData) => {
              // 提取月份数字
              let month = monthData.label;
              if (month.includes("-")) {
                month = month.split("-")[1];
              }
              // 去掉前导零
              month = parseInt(month).toString();

              // 查找该月份中的对应分类
              const categoryData = monthData.children
                ? monthData.children.find((item) => item.label === category)
                : null;

              if (categoryData) {
                monthValues[month] = categoryData.value;
              }
            });

            // 按月份顺序填充数据
            const data = allMonths.map((month) => monthValues[month]);
            seriesData.push(data);
          });

          // 格式化X轴标签
          const xAxisData = allMonths.map((month) => month + "月");

          // 更新图表
          diyecharts.updateLineChart(
            this,
            seriesData,
            xAxisData,
            timeType,
            categories
          );
        } else if (timeType == 3) {
          // 自然周数据处理
          // 提取所有周数并排序
          const allWeeks = new Set();
          res.data.forEach((weekData) => {
            let week = weekData.label;
            if (week.includes("-")) {
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();
            allWeeks.add(week);
          });
          const sortedWeeks = Array.from(allWeeks).sort(
            (a, b) => parseInt(a) - parseInt(b)
          );

          // 提取所有投诉分类
          const allCategories = new Set();
          res.data.forEach((weekData) => {
            if (weekData.children && weekData.children.length > 0) {
              weekData.children.forEach((category) => {
                allCategories.add(category.label);
              });
            }
          });
          const categories = Array.from(allCategories);

          // 为每个分类创建一个数据系列
          const seriesData = [];
          categories.forEach((category) => {
            const weekValues = {};

            // 初始化所有周的数据为0
            sortedWeeks.forEach((week) => {
              weekValues[week] = 0;
            });

            // 填充已有的周数据
            res.data.forEach((weekData) => {
              // 提取周数
              let week = weekData.label;
              if (week.includes("-")) {
                week = week.split("-")[1];
              }
              // 去掉前导零
              week = parseInt(week).toString();

              // 查找该周中的对应分类
              const categoryData = weekData.children
                ? weekData.children.find((item) => item.label === category)
                : null;

              if (categoryData) {
                weekValues[week] = categoryData.value;
              }
            });

            // 按周顺序填充数据
            const data = sortedWeeks.map((week) => weekValues[week]);
            seriesData.push(data);
          });

          // 格式化X轴标签
          const xAxisData = sortedWeeks.map((week) => week + "周");

          // 更新图表
          diyecharts.updateLineChart(
            this,
            seriesData,
            xAxisData,
            timeType,
            categories
          );
        } else {
          // 其他情况，显示无数据
          diyecharts.updateLineChart(this, [], [], timeType, ["暂无数据"]);
        }
      })
      .catch((error) => {
        console.error("获取投诉分类数据失败:", error);
        // 显示错误信息
        if (this.chartInstanceLine) {
          this.chartInstanceLine.hideLoading();
          this.chartInstanceLine.setOption({
            graphic: {
              type: "text",
              left: "center",
              top: "middle",
              style: {
                text: "加载数据失败",
                fill: "#999",
                fontSize: 16,
              },
            },
          });
        }
      });
  },

  RegionGetList() {
    request
      .get({
        url: "Region/GetList",
        load: false,
      })
      .then((res) => {
        if (res.data && res.data.length > 0) {
          // 获取区域数据
          const regions = res.data;

          // 创建区域选择列表
          const regionLabels = regions.map((item) => item.label);
          const regionColumns = [{ values: regionLabels }];

          // 默认选择第一个区域
          const firstRegion = regions[0].label;

          this.setData({
            regioninfo: regions,
            regionColumns: regionColumns,
            selectedRegion: firstRegion,
            selectedRegionValue: regions[0].value,
            regionIndex: 0,
          });

          this.init1();
        }
      });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 计算当前年份对应的yearIndex
    const currentYear = new Date().getFullYear().toString();
    const yearValues = this.data.yearColumns[0].values;
    const yearIndex = yearValues.indexOf(currentYear);

    // 如果找到当前年份在数组中的位置，则更新yearIndex
    if (yearIndex !== -1) {
      this.setData({
        yearIndex: yearIndex,
      });
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 先设置图表初始化函数，但不会立即执行
    this.setData(
      {
        pieec: {
          onInit: (canvas, width, height, dpr) => {
            // 立即保存实例引用
            this.chartInstancePie = diyecharts.initPieChart(
              this,
              canvas,
              width,
              height,
              dpr
            );
            return this.chartInstancePie;
          },
        },
        lineec: {
          onInit: (canvas, width, height, dpr) => {
            // 立即保存实例引用
            this.chartInstanceLine = diyecharts.initLineChart(
              this,
              canvas,
              width,
              height,
              dpr
            );
            return this.chartInstanceLine;
          },
        },
        barec: {
          onInit: (canvas, width, height, dpr) => {
            // 立即保存实例引用
            this.chartInstanceStackedBar = diyecharts.initHorizontalStackedBar(
              this,
              canvas,
              width,
              height,
              dpr
            );
            return this.chartInstanceStackedBar;
          },
        },
        userInfo: wx.getStorageSync("userInfo"),
        isnew: app.globalData.isnew,
        level: app.globalData.level,
        shouldHideTabs: rolePermission.shouldHideTabs(wx.getStorageSync("userInfo"))
      },
      () => {
        if(!this.data.isnew){
          // 只有在已登录状态下才加载数据
          this.RegionGetList();
          this.init();
        }
      }
    );
  },

  // 登录按钮点击事件
  login() {
    wx.navigateTo({
      url: "/pages/login/login",
    });
  },
  onUnload() {
    diyecharts.onUnload(this);
  },
  init() {
    switch (this.data.value) {
      case 0:
        this.ComplaintResolutionRateGetSummary();
        this.ComplaintResolutionRateGetServiceReport();
        this.ComplaintResolutionRateGetServiceReport1();
        break;
      case 1:
        this.ComplaintRateGetSummary();
        this.ComplaintRateGetServiceReport();
        break;
      case 2:
        this.ComplaintCategoriesGetSummary();
        this.ComplaintCategoriesGetServiceReport();
        break;
      case 3:
          this.RootCauseAnalysisGetSummary();
        //this.RootCauseAnalysisGetUserRootCause();
        this.RootCauseAnalysisGetServiceReport();
        break;
      default:
        // 默认处理
        break;
    }
  },
  init1() {
    switch (this.data.value) {
      case 0:
        this.ComplaintResolutionRateGetStat();
        break;
      case 1:
        this.ComplaintRateGetStat();
        break;
      case 2:
        this.ComplaintCategoriesGetStat();
        break;
    case 3:
        this.RootCauseAnalysisGetStat();
        break;
      default:
        // 默认处理
        break;
    }
  },

  //顶部选择日期范围的事件
  onOptionChange(e) {
    var value = e.detail;
    this.setData({
      value: value,
    });
    this.init();
    this.init1();
  },

  startTimePopup() {
    var time = new Date(this.data.startTime).getTime();
    this.setData({
      timeshow: true,
      currentDate: time,
      tType: 1,
    });
  },

  endTimePopup() {
    var time = new Date(this.data.endTime).getTime();
    this.setData({
      timeshow: true,
      currentDate: time,
      tType: 2,
    });
  },

  onConfirm(e) {
    console.log(e.detail);
    this.setData({
      timeshow: false,
      currentDate: e.detail,
    });
    if (this.data.tType == 1) {
      this.setData({
        startTime: dateUtil.getDateObject(e.detail),
      });
    } else {
      this.setData({
        endTime: dateUtil.getDateObject(e.detail),
      });
    }
    this.init();
  },

  onClose() {
    this.setData({
      timeshow: false,
    });
  },

  // 统计 事件

  // 显示年份选择弹出层

  ontjChange(e) {
    const index = e.detail.index;
    // 更新 active 和 timeType 值
    this.setData({
      active: index,
      timeType: index + 1, // 设置 timeType: 1-年度，2-自然月，3-自然周
    });
    // 切换选项卡后重新获取统计数据
    this.init1();
  },

  // 显示年份选择弹出层
  showYearPopup() {
    this.setData({
      yearshow: true,
    });
  },

  // 确认选择年份
  onYearConfirm(e) {
    const { value, index } = e.detail;
    const year = parseInt(value[0]);

    this.setData({
      selectedYear: year,
      yearIndex: index[0],
      yearshow: false,
    });

    // 选择年份后重新获取统计数据
    this.init1();
  },

  // 取消选择年份
  onYearCancel() {
    this.setData({
      yearshow: false,
    });
  },
  // 显示区域选择弹出层
  showRegionPopup() {
    this.setData({
      regionshow: true,
    });
  },

  // 确认选择区域
  onRegionConfirm(e) {
    const { value, index } = e.detail;
    const regionLabel = value[0];

    // 找到对应的区域对象
    const selectedRegion = this.data.regioninfo.find(
      (item) => item.label === regionLabel
    );

    if (selectedRegion) {
      this.setData({
        selectedRegion: selectedRegion.label,
        selectedRegionValue: selectedRegion.value,
        regionIndex: index[0],
        regionshow: false,
      });
      // 选择年份后重新获取统计数据
      this.init1();
    }
  },

  // 取消选择区域
  onRegionCancel() {
    this.setData({
      regionshow: false,
    });
  },

  // 关闭区域选择弹出层
  onRegionClose() {
    this.setData({
      regionshow: false,
    });
  },

  onsr1Change(e) {
    const index = e.detail.index;
    // 更新 active 和 timeType 值
    this.setData({
      sractive1: index,
    });
    switch (this.data.value) {
      case 0:
        this.ComplaintResolutionRateGetServiceReport();
        break;
      case 1:
        this.ComplaintRateGetServiceReport();
        break;
      default:
        // 默认处理
        break;
    }
  },

  /**
   * 更新显示的数据（分页处理）- 第一个列表
   */
  updateDisplayedData() {
    const allData = this.data.serviceReportinfoAll;
    const pageSize = this.data.pageSize;

    if (allData.length <= pageSize) {
      // 数据不超过10条，显示全部，不显示任何按钮
      this.setData({
        serviceReportinfoDisplayed: allData,
        serviceReportinfo: allData,
        showMoreButton: false,
        showCollapseButton: false,
        isExpanded: false
      });
    } else {
      // 数据超过10条，根据当前展开状态决定显示内容
      if (this.data.isExpanded) {
        // 已展开状态：显示全部数据，显示"收起"按钮
        this.setData({
          serviceReportinfoDisplayed: allData,
          serviceReportinfo: allData,
          showMoreButton: false,
          showCollapseButton: true
        });
      } else {
        // 未展开状态：只显示前10条，显示"查看更多"按钮
        this.setData({
          serviceReportinfoDisplayed: allData.slice(0, pageSize),
          serviceReportinfo: allData.slice(0, pageSize),
          showMoreButton: true,
          showCollapseButton: false
        });
      }
    }
  },

  /**
   * 更新显示的数据（分页处理）- 第二个列表
   */
  updateDisplayedData1() {
    const allData = this.data.serviceReportinfo1All;
    const pageSize = this.data.pageSize;

    if (allData.length <= pageSize) {
      // 数据不超过10条，显示全部，不显示任何按钮
      this.setData({
        serviceReportinfo1Displayed: allData,
        serviceReportinfo1: allData,
        showMoreButton1: false,
        showCollapseButton1: false,
        isExpanded1: false
      });
    } else {
      // 数据超过10条，根据当前展开状态决定显示内容
      if (this.data.isExpanded1) {
        // 已展开状态：显示全部数据，显示"收起"按钮
        this.setData({
          serviceReportinfo1Displayed: allData,
          serviceReportinfo1: allData,
          showMoreButton1: false,
          showCollapseButton1: true
        });
      } else {
        // 未展开状态：只显示前10条，显示"查看更多"按钮
        this.setData({
          serviceReportinfo1Displayed: allData.slice(0, pageSize),
          serviceReportinfo1: allData.slice(0, pageSize),
          showMoreButton1: true,
          showCollapseButton1: false
        });
      }
    }
  },

  /**
   * 查看更多文本点击事件 - 第一个列表
   */
  onShowMore() {
    // 设置为展开状态
    this.setData({
      isExpanded: true
    });

    // 重新处理显示数据
    this.updateDisplayedData();
  },

  /**
   * 收起文本点击事件 - 第一个列表
   */
  onCollapse() {
    // 设置为收起状态
    this.setData({
      isExpanded: false
    });

    // 重新处理显示数据
    this.updateDisplayedData();
  },

  /**
   * 查看更多文本点击事件 - 第二个列表
   */
  onShowMore1() {
    // 设置为展开状态
    this.setData({
      isExpanded1: true
    });

    // 重新处理显示数据
    this.updateDisplayedData1();
  },

  /**
   * 收起文本点击事件 - 第二个列表
   */
  onCollapse1() {
    // 设置为收起状态
    this.setData({
      isExpanded1: false
    });

    // 重新处理显示数据
    this.updateDisplayedData1();
  },

  onsr2Change(e) {
    const index = e.detail.index;
    // 更新 active 和 timeType 值
    this.setData({
      sractive2: index,
    });
    this.ComplaintResolutionRateGetServiceReport1()
  },
});
