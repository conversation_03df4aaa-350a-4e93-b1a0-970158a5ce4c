// pages/dataOverview/index.js
const dateUtil = require("../../utils/dateUtil.js");
const request = require("../../utils/request.js");
const diyecharts = require("../../utils/diyecharts.js");
const app = getApp();

// 定义常量
const CHANNEL_COLORS = {
  完工率: "#1677ff", // 蓝色
  满意度: "#52c41a", // 绿色
  录入率: "#faad14", // 黄色
  投诉完成率: "#ff4d4f", // 红色
  满意评价率: "#fa541c",
  接单及时率: "#52c41a",
  default: "#722ed1", // 紫色
};
Page({
  /**
   *
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    isnew: false, //判断是否新用户
    timeshow: false,
    minDate: new Date(2015, 0, 1).getTime(),
    maxDate: new Date(2035, 11, 1).getTime(),
    currentDate: "",
    startTime: dateUtil.getStartMonthDate(),
    endTime: dateUtil.getEndMonthDate(),
    tType: 1,
    pieec: {
      onInit: null,
    },
    barec: {
      onInit: null,
    },
    summaryinfo: [],
    channelinfo: [],
    channelinfoloading: true,
    summaryLoading: true, // 工单概况加载状态
  },

  /**
   * 获取工单类型数据
   * @returns {Promise} 请求Promise
   */
  HomeGetServiceFormType() {
    // 设置加载状态
    this.setData({
      summaryLoading: true,
    });
    const data = [this.data.startTime, this.data.endTime];
    request
      .post({
        url: "Home/GetServiceFormType",
        data,
        load: false, // 不使用全局loading，而是使用组件内的loading状态
      })
      .then((res) => {
        this.setData({
          summaryinfo: res.data,
          summaryLoading: false,
        });
      })
      .catch((err) => {
        this.setData({
          summaryinfo: [],
          summaryLoading: false,
        });
      });
  },

  /**
   * 获取KPI摘要数据
   * @returns {Promise} 请求Promise
   */
  HomeGetKPISummary() {
    // 设置加载状态
    this.setData({
      channelinfoloading: true,
    });

    const data = [this.data.startTime, this.data.endTime];
    request
      .post({
        url: "Home/GetKPISummary",
        data,
        load: false, // 不使用全局loading，而是使用组件内的loading状态
      })
      .then((res) => {
        // 处理数据，添加颜色和格式化百分比
        const processedData = res.data.map((item) => {
          // 计算百分比并四舍五入到整数
          const ratePercent = Math.round(item.rate * 100);

          // 获取该渠道对应的颜色，如果没有定义则使用默认颜色
          const color = CHANNEL_COLORS[item.title] || CHANNEL_COLORS.default;

          // 返回处理后的数据
          return {
            ...item,
            rateText: ratePercent + "%", // 格式化为百分比字符串
            color: color, // 添加颜色字段
          };
        });

        this.setData({
          channelinfo: processedData,
          channelinfoloading: false,
        });
      })
      .catch((err) => {
        this.setData({
          channelinfo: [],
          channelinfoloading: false,
        });
      });
  },

  /**
   * 获取KPI数据并更新柱状图
   * @returns {Promise} 请求Promise
   */
  HomeGetKPI() {
    // 创建图表实例守卫
    const ensureBarReady = diyecharts.createChartGuard(
      this,
      "chartInstanceBar"
    );
    // 先确保图表实例已创建
    return ensureBarReady()
      .then(() => {
        // 显示加载动画
        diyecharts.showLoading(this, "chartInstanceBar");
        const data = [this.data.startTime, this.data.endTime];
        // 发起请求
        return request.post({
          url: "Home/GetKPI",
          data,
          load: false, // 不使用全局loading，而是使用组件内的loading状态
        });
      })
      .then(res => {
        // 检查响应数据是否存在
        if (res && res.data && res.data.length > 0) {
          // 提取需要的数据：title、rate、pddRate
          const titles = res.data.map(item => item.title);
          const rateData = res.data.map(item => Math.round(item.rate * 100)); // 转为百分比
          const pddRateData = res.data.map(item => Math.round(item.pddRate * 100)); // 转为百分比

          // 使用diyecharts中的方法更新图表
          diyecharts.updateBarChart(this, titles, rateData, pddRateData);


          return res.data;
        } else {

          if (this.chartInstanceBar) {
            this.chartInstanceBar.hideLoading();
          }

          this.setData({
            barec: null,
          });

          return [];
        }
      })
      .catch(err => {
        // 处理错误

        if (this.chartInstanceBar) {
          this.chartInstanceBar.hideLoading();
        }

        this.setData({

          barec: null
        });

        return [];
      });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 先检查登录状态
    this.setData(
      {
        isnew: app.globalData.isnew
      },
      () => {
        if (!this.data.isnew) {
          // 已登录状态下才设置图表初始化函数并加载数据
          this.setData(
            {
              barec: {
                onInit: (canvas, width, height, dpr) => {
                  // 立即保存实例引用
                  this.chartInstanceBar = diyecharts.initBarChart(
                    this,
                    canvas,
                    width,
                    height,
                    dpr
                  );
                  return this.chartInstanceBar;
                },
              },
              // 重置加载状态，准备加载数据
              summaryLoading: true,
              channelinfoloading: true,
            },
            () => {
              // 初始化并加载数据
              this.init();
            }
          );
        } else {
          // 未登录状态下，重置所有状态
          this.setData({
            barec: null,
            summaryinfo: [],
            channelinfo: [],
            summaryLoading: false,
            channelinfoloading: false,

          });
        }
      }
    );
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 使用diyecharts中的方法卸载图表实例
    diyecharts.onUnload(this);
  },

  /**
   * 初始化并加载所有数据
   */
  init() {
    this.HomeGetServiceFormType(), this.HomeGetKPISummary(), this.HomeGetKPI();
  },

  login() {
    wx.navigateTo({
      url: "/pages/login/login",
    });
  },

  /**
   * 顶部选择日期范围的事件
   * @param {Object} e 事件对象
   */
  onOptionChange(e) {
    this.setData(
      {
        value: e.detail,
      },
      () => {
        this.init();
      }
    );
  },

  /**
   * 显示开始时间选择器
   */
  startTimePopup() {
    const time = new Date(this.data.startTime).getTime();
    this.setData({
      timeshow: true,
      currentDate: time,
      tType: 1,
    });
  },

  /**
   * 显示结束时间选择器
   */
  endTimePopup() {
    const time = new Date(this.data.endTime).getTime();
    this.setData({
      timeshow: true,
      currentDate: time,
      tType: 2,
    });
  },

  /**
   * 日期选择确认事件
   * @param {Object} e 事件对象
   */
  onConfirm(e) {
    // 先关闭弹窗
    this.setData({
      timeshow: false,
      currentDate: e.detail,
    });

    // 根据类型设置开始或结束时间
    const dateObj = dateUtil.getDateObject(e.detail);
    const updateData = {};

    if (this.data.tType === 1) {
      updateData.startTime = dateObj;
    } else {
      updateData.endTime = dateObj;
    }

    // 更新时间并重新加载数据
    this.setData(updateData, () => {
      this.init();
    });
  },

  /**
   * 关闭日期选择器
   */
  onClose() {
    this.setData({
      timeshow: false,
    });
  },
});