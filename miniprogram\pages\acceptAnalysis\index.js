/**
 * 接单及时性分析页面
 * 从workorderAnalysis页面中提取的独立页面
 */

var dateUtil = require("../../utils/dateUtil.js");
const request = require("../../utils/request.js");
const ToastDialog = require("../../utils/toastDialog.js");
var diyecharts = require("../../utils/diyecharts.js");
const rolePermission = require("../../utils/rolePermission.js");
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    level: 0,
    isnew: false, // 判断是否新用户（未登录）
    timeshow: false,
    yearshow: false,
    minDate: new Date(2015, 0, 1).getTime(),
    maxDate: new Date(2035, 11, 1).getTime(),
    currentDate: "",
    currentYearDate: new Date().getTime(),
    startTime: dateUtil.getLast30DaysStartDate(),
    endTime: dateUtil.getLast30DaysEndDate(),
    selectedYear: new Date().getFullYear(),
    active: 1, // 默认选中"年度"选项卡
    sRactive1: 0, // 默认选中"全部"选项卡
    sractive: 0,
    tType: 1,
    timeType: 1,

    // 页面标题配置
    pageTitle: "接单及时性分析",
    summaryTitle: "工单汇总",
    serviceReportTitle: "接单及时率",
    timeoutTitle: "接单超时率",
    chartTitle: "接单及时率",

    // 年份选择配置
    yearColumns: [{
      values: [
        "2020", "2021", "2022", "2023", "2024",
        "2025", "2026", "2027", "2028", "2029", "2030",
      ],
    }],
    yearIndex: 0,
    yearshow: false,

    // 区域选择配置
    regioninfo: [],
    selectedRegion: "暂无",
    selectedRegionValue: "",
    regionIndex: 0,
    regionshow: false,

    // 数据存储
    summaryinfo: [],
    serviceReportinfo1: [], // 及时率数据
    serviceReportinfo2: [], // 超时率数据

    // 图表配置
    pieec: { onInit: null },
    lineec: { onInit: null },

    // 加载状态
    summaryinfoloading: true,
    serviceReportinfo1loading: true,
    serviceReportinfowloading: true,

    // 权限控制
    shouldHideTabs: false
  },

  /**
   * 获取区域列表数据
   */
  RegionGetList() {
    request
      .get({
        url: "Region/GetList",
        load: false,
      })
      .then((res) => {
        if (res.data && res.data.length > 0) {
          // 获取区域数据
          const regions = res.data;

          // 创建区域选择列表
          const regionLabels = regions.map((item) => item.label);
          const regionColumns = [{
            values: regionLabels
          }];

          // 默认选择第一个区域
          const firstRegion = regions[0].label;

          this.setData({
            regioninfo: regions,
            regionColumns: regionColumns,
            selectedRegion: firstRegion,
            selectedRegionValue: regions[0].value,
            regionIndex: 0,
          });

          this.loadStatData();
        }
      });
  },

  /**
   * 加载汇总数据（饼图）
   */
  loadSummaryData() {
    this.setData({
      summaryinfoloading: true
    });

    const ensurePieReady = diyecharts.createChartGuard(this, "chartInstancePie");
    diyecharts.showLoading(this, "chartInstancePie");

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };

    request
      .post({
        url: "ServiceFormAccept/GetSummary",
        data,
        load: false,
      })
      .then(async (res) => {
        this.setData({
          summaryinfoloading: false,
          summaryinfo: res.data,
        });

        await ensurePieReady(); // 等待图表实例创建

        // 处理饼图数据
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));

        diyecharts.updatePieChart(this, piedata, this.data.summaryTitle);
      })
      .catch((error) => {
        console.error("获取接单汇总数据失败:", error);
        this.setData({ summaryinfoloading: false });
      });
  },

  /**
   * 加载及时率数据
   */
  loadServiceReportData() {
    this.setData({
      serviceReportinfo1loading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      type: 1,
    };

    request
      .post({
        url: "ServiceFormAccept/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data
          .filter((item) => item.label !== null) // 过滤掉label为null的项
          .map((item) => {
            // 计算百分比（用于进度条，需要是数值）
            const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
            return {
              ...item,
              ratePercent: ratePercentValue,
            };
          });

        this.setData({
          serviceReportinfo1: processedData,
          serviceReportinfo1loading: false
        });
      })
      .catch((err) => {
        console.error("获取接单及时率数据失败:", err);
        this.setData({
          serviceReportinfo1: [],
          serviceReportinfo1loading: false
        });
      });
  },

  /**
   * 加载超时率数据
   */
  loadTimeoutData() {
    this.setData({
      serviceReportinfowloading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
      type: 2,
      timeType: this.data.sRactive1,
    };

    request
      .post({
        url: "ServiceFormAccept/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data
          .filter((item) => item.label !== null) // 过滤掉label为null的项
          .map((item) => {
            const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
            return {
              ...item,
              ratePercent: ratePercentValue,
            };
          });

        this.setData({
          serviceReportinfo2: processedData,
          serviceReportinfowloading: false
        });
      })
      .catch((err) => {
        console.error("获取接单超时率数据失败:", err);
        this.setData({
          serviceReportinfo2: [],
          serviceReportinfowloading: false
        });
      });
  },

  /**
   * 加载统计数据（折线图）
   */
  loadStatData() {
    const ensureLineReady = diyecharts.createChartGuard(this, "chartInstanceLine");
    var data = {};
    var timeType = this.data.timeType;
    diyecharts.showLoading(this, "chartInstanceLine");

    // 使用全局权限工具获取value参数
    const value = rolePermission.getValueByRole(this.data.userInfo, this.data.selectedRegionValue);

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        value: value,
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level: this.data.level,
        timeType: timeType,
        value: value,
      };
    }

    request
      .post({
        url: "ServiceFormAccept/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
        await ensureLineReady(); // 等待图表实例创建

        // 处理折线图数据
        const { linedata, xAxisData } = this.processLineData(res.data, timeType);

        diyecharts.updateLineChart(this, linedata, xAxisData, timeType, this.data.chartTitle);
      })
      .catch((error) => {
        console.error("获取接单统计数据失败:", error);
      });
  },

  /**
   * 处理折线图数据
   * @param {Array} data - 原始数据
   * @param {number} timeType - 时间类型
   * @returns {Object} 包含linedata和xAxisData的对象
   */
  processLineData(data, timeType) {
    const linedata = [];
    const xAxisData = [];

    if (timeType == 2) {
      // 自然月处理
      const allMonths = Array.from({ length: 12 }, (_, i) => (i + 1).toString());
      const monthData = {};

      // 初始化所有月份的数据为0
      allMonths.forEach((month) => {
        monthData[month] = 0;
      });

      // 填充已有的月份数据
      data.forEach((e) => {
        let month = e.label;
        if (month.includes("-")) {
          month = month.split("-")[1];
        }
        month = parseInt(month).toString();
        let rate = parseFloat((e.rate * 100).toFixed(12));
        monthData[month] = rate;
      });

      // 按月份顺序填充数据
      allMonths.forEach((month) => {
        xAxisData.push(month + "月");
        linedata.push(monthData[month]);
      });
    } else if (timeType == 3) {
      // 自然周处理
      const weekData = [];
      data.forEach((e) => {
        let week = e.label;
        if (week.includes("-")) {
          week = week.split("-")[1];
        }
        week = parseInt(week).toString();
        let rate = parseFloat((e.rate * 100).toFixed(12));
        weekData.push({ week: week, rate: rate });
      });

      // 按周数排序
      weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

      // 填充排序后的数据
      weekData.forEach((item) => {
        xAxisData.push(item.week + "周");
        linedata.push(item.rate);
      });
    } else {
      // 其他类型（如年度）保持原样
      data.forEach((e) => {
        let formattedLabel = e.label;
        let rate = parseFloat((e.rate * 100).toFixed(12));
        xAxisData.push(formattedLabel);
        linedata.push(rate);
      });
    }

    return { linedata, xAxisData };
  },

  /**
   * 初始化数据加载
   */
  init() {
    this.loadSummaryData();
    this.loadServiceReportData();
    this.loadTimeoutData();
  },

  /**
   * 初始化统计数据加载
   */
  init1() {
    this.loadStatData();
  },

  /**
   * 开始时间选择弹窗
   */
  startTimePopup() {
    var time = new Date(this.data.startTime).getTime();
    this.setData({
      timeshow: true,
      currentDate: time,
      tType: 1,
    });
  },

  /**
   * 结束时间选择弹窗
   */
  endTimePopup() {
    var time = new Date(this.data.endTime).getTime();
    this.setData({
      timeshow: true,
      currentDate: time,
      tType: 2,
    });
  },

  /**
   * 时间选择确认
   */
  onConfirm(e) {
    this.setData({
      timeshow: false,
      currentDate: e.detail,
    });

    if (this.data.tType == 1) {
      this.setData({
        startTime: dateUtil.getDateObject(e.detail),
      });
    } else {
      this.setData({
        endTime: dateUtil.getDateObject(e.detail),
      });
    }

    // 重新加载数据
    this.init();
  },

  /**
   * 关闭时间选择弹窗
   */
  onClose() {
    this.setData({
      timeshow: false,
    });
  },

  /**
   * 显示年份选择弹出层
   */
  showYearPopup() {
    this.setData({
      yearshow: true,
    });
  },

  /**
   * 确认选择年份
   */
  onYearConfirm(e) {
    const { value, index } = e.detail;
    const year = parseInt(value[0]);
    this.setData({
      selectedYear: year,
      yearIndex: index[0],
      yearshow: false,
    });
    this.init1();
  },

  /**
   * 取消选择年份
   */
  onYearCancel() {
    this.setData({
      yearshow: false,
    });
  },

  /**
   * 统计时间类型切换事件
   */
  ontjChange(e) {
    const index = e.detail.index;
    this.setData({
      active: index,
      timeType: index + 1, // 设置 timeType: 1-年度，2-自然月，3-自然周
    });
    this.init1();
  },

  /**
   * 显示区域选择弹出层
   */
  showRegionPopup() {
    this.setData({
      regionshow: true,
    });
  },

  /**
   * 确认选择区域
   */
  onRegionConfirm(e) {
    const { value, index } = e.detail;
    const regionLabel = value[0];

    // 找到对应的区域对象
    const selectedRegion = this.data.regioninfo.find(
      (item) => item.label === regionLabel
    );

    if (selectedRegion) {
      this.setData({
        selectedRegion: selectedRegion.label,
        selectedRegionValue: selectedRegion.value,
        regionIndex: index[0],
        regionshow: false,
      });
      // 选择区域后重新获取统计数据
      this.init1();
    }
  },

  /**
   * 取消选择区域
   */
  onRegionCancel() {
    this.setData({
      regionshow: false,
    });
  },

  /**
   * 关闭区域选择弹出层
   */
  onRegionClose() {
    this.setData({
      regionshow: false,
    });
  },

  /**
   * 权限级别tab切换事件
   */
  onsrChange(e) {
    this.setData({
      sractive: e.detail.index
    });
    // 重新加载服务报告和超时数据
    this.loadServiceReportData();
    this.loadTimeoutData();
  },

  /**
   * 超时时间类型切换事件
   */
  onSRChange1(e) {
    this.setData({
      sRactive1: e.detail.index,
    });
    // 重新加载超时数据
    this.loadTimeoutData();
  },

  /**
   * 登录按钮点击事件
   */
  login() {
    wx.navigateTo({
      url: "/pages/login/login",
    });
  },

  /**
   * 返回按钮点击事件
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 计算当前年份对应的yearIndex
    const currentYear = new Date().getFullYear().toString();
    const yearValues = this.data.yearColumns[0].values;
    const yearIndex = yearValues.indexOf(currentYear);

    // 如果找到当前年份在数组中的位置，则更新yearIndex
    if (yearIndex !== -1) {
      this.setData({
        yearIndex: yearIndex,
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 先设置图表初始化函数
    this.setData({
      pieec: {
        onInit: (canvas, width, height, dpr) => {
          this.chartInstancePie = diyecharts.initPieChart(this, canvas, width, height, dpr);
          return this.chartInstancePie;
        },
      },
      lineec: {
        onInit: (canvas, width, height, dpr) => {
          this.chartInstanceLine = diyecharts.initLineChart(this, canvas, width, height, dpr);
          return this.chartInstanceLine;
        },
      },
      userInfo: wx.getStorageSync("userInfo"),
      isnew: app.globalData.isnew,
      level: app.globalData.level,
      shouldHideTabs: rolePermission.shouldHideTabs(wx.getStorageSync("userInfo"))
    }, () => {
      if (!this.data.isnew) {
        // 只有在已登录状态下才加载数据
        this.RegionGetList();
        this.init();
      }
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    diyecharts.onUnload(this);
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
});
