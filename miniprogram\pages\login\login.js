const request = require('../../utils/request.js');
const ToastDialog = require('../../utils/toastDialog.js')
const app = getApp()
var that;
Page({
  /**
   * 页面的初始数据
   */
  data: {
    imageUrl: app.globalData.imageUrl,
    agree: false,
    familyId: 0,
  },
  checkboxChange(e) {
    that.setData({
      agree: !that.data.agree, // 更新 checkbox 的选中状态
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    that = this
    that.setData({
      familyId: options.familyId,
    })
  },
  //《隐私政策》
  privacypolicy() {
    wx.navigateTo({
      url: '/pages/login/privacypolicy/privacypolicy',
    })
  },
  //《用户协议》
  agree() {
    wx.navigateTo({
      url: '/pages/login/agreement/agreement',
    })
  },
  //一键登录
  submit(e) {
    if (e.detail.iv == undefined) {
        wx.navigateTo({
          url: '/pages/index/index',
        }) 
    }else{
    wx.login({
      success: ressu => {
        var data = {
          loginCode: ressu.code,
          phoneCode: e.detail.code,
        }
        request.post({
            url: 'SysAccount/OCLoginNew',
            data,
            loadmsg:"登录中"
          })
          .then(res => {
            app.globalData.token = res.data.token
            ToastDialog.TSuccess("登录成功",()=>{
                  wx.navigateTo({
                    url: '/pages/index/index',
                  })   
             })
          })
      }
    })
  }
  },
  back(){
    wx.navigateBack({
      delta: 1
    });
  }
})