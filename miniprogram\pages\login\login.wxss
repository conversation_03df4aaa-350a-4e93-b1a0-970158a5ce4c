/* pages/Home/Index/index.wxss */

page{
  background-color: white;
}
.loginpage{
  width: 100%;
  height: 100vh;
  background-color: white; 
  background-repeat: no-repeat;
  background-size: 100%, 100%;
  display:flex;                    
  justify-content: center;  
}

.form{
  padding-top: 300rpx;
  height: 320rpx;
  width: 80%;
}
.van-tabs__scroll {
  background-color:transparent !important;
}
.form-padding{
  padding: 0 50rpx 20rpx 50rpx;
}
.form-name{
  font-size: 22rpx;
  color: #999;
  margin: 15rpx 0;
}
.form-input{
  border: 1px solid #c0c8cd;
  background-color: #f9fafe;
  border-radius: 10rpx;
  display:flex;
  padding: 10rpx;
  align-items: center;
}
.form-input-img image{
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}
image{
  margin: 0;
  width: 100%;
  height: auto;
}
/* .title{
  margin-left: 35%;
} */
.inputMobile{
  width: 50%;
}
.display{
  display: inline-block;
}
.line{
  line-height: 40px;
}
.imgf{
   height:380rpx;
   width: 100%;
}
.cu-form-group .title {
  min-width: calc(4em + 30rpx);
}

.text{

  background-image: linear-gradient(#556eb1, #0081ff);

  background-clip: text;
  
  -webkit-background-clip: text;
  
  color: transparent; /*需要文字透明*/
}
.logininput{
  border: 1px solid #ccc;
  padding: 10rpx;
  border-radius: 4px;
}
.backs{
  position: absolute;
  left: 30rpx;
  top:100rpx;
  color: #fff;
  
}
