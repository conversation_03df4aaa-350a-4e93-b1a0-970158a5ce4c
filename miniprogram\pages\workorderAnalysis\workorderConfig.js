/**
 * 工单分析配置文件
 * 统一管理所有分析类型的配置信息
 */

// 分析类型配置
const ANALYSIS_TYPES = {
  ACCEPT: 0,      // 接单及时性分析
  DISPATCH: 1,    // 派工及时性分析
  FINISHED: 2,    // 完工及时性分析
  RECORD: 3,      // 录单率分析
  REMIND: 4,      // 催单率分析
  REPAIR: 5,      // 一次修复率分析
  INST_RECT: 6    // 安装整改完成率分析
};

// 分析选项配置
const ANALYSIS_OPTIONS = [
  {
    text: "接单及时性分析",
    value: ANALYSIS_TYPES.ACCEPT,
  },
  {
    text: "派工及时性分析",
    value: ANALYSIS_TYPES.DISPATCH,
  },
  {
    text: "完工及时性分析",
    value: ANALYSIS_TYPES.FINISHED,
  },
  {
    text: "录单率分析",
    value: ANALYSIS_TYPES.RECORD,
  },
  {
    text: "催单率分析",
    value: ANALYSIS_TYPES.REMIND,
  },
  {
    text: "一次修复率分析",
    value: ANALYSIS_TYPES.REPAIR,
  },
  {
    text: "安装整改完成率分析",
    value: ANALYSIS_TYPES.INST_RECT,
  },
];

// API端点配置
const API_ENDPOINTS = {
  [ANALYSIS_TYPES.ACCEPT]: {
    summary: "ServiceFormAccept/GetSummary",
    serviceReport: "ServiceFormAccept/GetServiceReport",
    stat: "ServiceFormAccept/GetStat"
  },
  [ANALYSIS_TYPES.DISPATCH]: {
    summary: "ServiceFormDispatch/GetSummary",
    serviceReport: "ServiceFormDispatch/GetServiceReport",
    stat: "ServiceFormDispatch/GetStat"
  },
  [ANALYSIS_TYPES.FINISHED]: {
    summary: "ServiceFormFinished/GetSummary",
    serviceReport: "ServiceFormFinished/GetServiceReport",
    stat: "ServiceFormFinished/GetStat",
    serviceOutReport: "ServiceFormFinished/GetServiceOutReport"
  },
  [ANALYSIS_TYPES.RECORD]: {
    summary: "ServiceFormRecord/GetSummary",
    serviceReport: "ServiceFormRecord/GetServiceReport",
    stat: "ServiceFormRecord/GetStat"
  },
  [ANALYSIS_TYPES.REMIND]: {
    summary: "ServiceFormRemind/GetSummary",
    serviceReport: "ServiceFormRemind/GetServiceReport",
    stat: "ServiceFormRemind/GetStat"
  },
  [ANALYSIS_TYPES.REPAIR]: {
    summary: "ServiceFormRepair/GetSummary",
    serviceReport: "ServiceFormRepair/GetServiceReport",
    stat: "ServiceFormRepair/GetStat"
  },
  [ANALYSIS_TYPES.INST_RECT]: {
    summary: "ServiceFormInstRect/GetSummary",
    serviceReport: "ServiceFormInstRect/GetServiceReport",
    stat: "ServiceFormInstRect/GetStat"
  }
};

// 标题配置
const TITLES = {
  summary: [
    "工单汇总",      // 接单
    "工单汇总",      // 派工
    "完工率汇总",    // 完工
    "录单汇总",      // 录单
    "催单汇总",      // 催单
    "一次修复汇总",  // 一次修复
    "安装整改完成汇总", // 安装整改
  ],
  serviceReport: [
    "接单及时率",    // 接单
    "派工及时性",    // 派工
    "完工率",        // 完工
    "录单率",        // 录单
    "催单率",        // 催单
    "一次修复率",    // 一次修复
    "安装整改完成率", // 安装整改
  ],
  timeout: [
    "接单超时率",    // 接单
    "",              // 派工（无超时率）
    "完工超时率",    // 完工
    "",              // 录单（无超时率）
    "",              // 催单（无超时率）
    "",              // 一次修复（无超时率）
    "",              // 安装整改（无超时率）
  ],
  timeoutAccepted: [
    "",              // 接单（无已接单超时率）
    "",              // 派工（无已接单超时率）
    "完工超时率(已接单)", // 完工
    "",              // 录单（无已接单超时率）
    "",              // 催单（无已接单超时率）
    "",              // 一次修复（无已接单超时率）
    "",              // 安装整改（无已接单超时率）
  ],
  chart: [
    "接单及时率",    // 接单
    "派工及时性",    // 派工
    "完工率",        // 完工
    "录单率",        // 录单
    "催单率",        // 催单
    "一次修复率",    // 一次修复
    "安装整改完成率", // 安装整改
  ]
};

// 特殊配置 - 哪些分析类型支持超时率分析
const SUPPORTS_TIMEOUT = [ANALYSIS_TYPES.ACCEPT, ANALYSIS_TYPES.FINISHED];

// 特殊配置 - 哪些分析类型支持已接单超时率分析
const SUPPORTS_TIMEOUT_ACCEPTED = [ANALYSIS_TYPES.FINISHED];

// 特殊配置 - 完工分析的特殊数据字段
const SPECIAL_DATA_FIELDS = {
  [ANALYSIS_TYPES.FINISHED]: {
    summaryValueField: 'ppd' // 完工分析使用ppd字段而不是quantity
  }
};

// API参数配置
const API_PARAMS = {
  serviceReport: {
    [ANALYSIS_TYPES.ACCEPT]: { type: 1 },
    [ANALYSIS_TYPES.DISPATCH]: {},
    [ANALYSIS_TYPES.FINISHED]: {},
    [ANALYSIS_TYPES.RECORD]: { type: 1 },
    [ANALYSIS_TYPES.REMIND]: { type: 1 },
    [ANALYSIS_TYPES.REPAIR]: { type: 1 },
    [ANALYSIS_TYPES.INST_RECT]: { type: 1 }
  },
  serviceOutReport: {
    [ANALYSIS_TYPES.ACCEPT]: {
      timeout: { type: 2 }
    },
    [ANALYSIS_TYPES.FINISHED]: {
      timeout: {},
      timeoutAccepted: { type: 1 }
    }
  }
};

module.exports = {
  ANALYSIS_TYPES,
  ANALYSIS_OPTIONS,
  API_ENDPOINTS,
  TITLES,
  SUPPORTS_TIMEOUT,
  SUPPORTS_TIMEOUT_ACCEPTED,
  SPECIAL_DATA_FIELDS,
  API_PARAMS
};
