/* pages/dataOverview/index.wxss */
.wrapper {
  background-color: white;
  padding: 32rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.wrapper__center {
  color: var(--td-text-color-placeholder);
}
.wrapper__left,
.wrapper__right {
  width: 240rpx;
  color: var(--td-text-color-primary);
  font-size: 32rpx;
  font-weight: 600;
  line-height: 48rpx;
}
.wrapper__right {
  text-align: right;
}
.view-d{
  background-color: white;
  border-radius: 10rpx;
  margin: 10rpx 0;
}
.container {
  height: 600rpx; /* 使用视口高度 */
}