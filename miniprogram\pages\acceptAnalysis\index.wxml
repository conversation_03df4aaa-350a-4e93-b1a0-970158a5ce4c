<!--pages/acceptAnalysis/index.wxml-->
<van-sticky>
  <nav-bar title="{{pageTitle}}" />
  <view class="text-font-14 flex-row bg-color-white text-align-center padding-tb-5">
    <view class="flex-item-2 padding-left-10">
      <van-button size="small" type="default" bind:tap="goBack">
        <van-icon name="arrow-left" size="16px" />
        返回
      </van-button>
    </view>
    <view class="flex-item-6 flex-row">
      <view class="padding-left-10">
        <van-icon name="notes-o" size="18px" />
      </view>
      <view class="padding-left-5" bindtap="startTimePopup">{{startTime}}</view>
      <view class="padding-left-5">~</view>
      <view class="padding-left-5" bindtap="endTimePopup">{{endTime}}</view>
    </view>
  </view>
</van-sticky>

<!-- 未登录状态 -->
<view wx:if="{{isnew}}">
  <van-empty description="暂无数据，请前往登录" image="search" />
  <view class="padding-lr-50">
    <van-button type="info" round block bind:tap="login">查看数据</van-button>
  </view>
</view>

<!-- 已登录状态 -->
<view wx:else class="padding-lr-10">
  <!-- 工单汇总（饼图） -->
  <view class="padding-top-10 padding-bottom-5 text-font-13 text-color-textgray">
    <van-icon name="description-o" color="#1677ff" class="padding-right-5" />{{summaryTitle}}
  </view>
  <view class="view-d">
    <view wx:if="{{summaryinfoloading}}" class="padding-tb-20 text-align-center">
      <van-loading size="24px" color="#1677ff">加载中...</van-loading>
    </view>
    <view wx:elif="{{summaryinfo.length === 0}}" class="padding-tb-20 text-align-center">
      <van-empty description="暂无数据" image="search" />
    </view>
    <view wx:else>
      <view class="text-font-10">
        <van-grid column-num="3">
          <van-grid-item use-slot wx:for="{{summaryinfo}}" wx:key="index">
            <view>
              <view>{{item.title}}</view>
              <view class="padding-tb-5 text-font-bold">
                <text class="text-font-20">{{item.quantity}}</text> 单
              </view>
              <view class="text-color-content"> 同比上期
                <text class="text-color-red" wx:if="{{item.rate<0}}">{{item.rate}}%</text>
                <text class="text-color-success" wx:else>+{{item.rate}}%</text>
              </view>
            </view>
          </van-grid-item>
        </van-grid>
      </view>
      <view style="height: 600rpx;">
        <ec-canvas ec="{{ pieec }}"></ec-canvas>
      </view>
    </view>
  </view>

  <!-- 权限级别tab切换 -->
  <van-tabs wx:if="{{!shouldHideTabs}}" active="{{ sractive }}" bind:change="onsrChange" color="#1677ff">
    <van-tab title="区域"></van-tab>
    <van-tab title="城市"></van-tab>
    <van-tab title="门店"></van-tab>
    <van-tab title="工程师"></van-tab>
    <van-tab title="调度员"></van-tab>
  </van-tabs>

  <!-- 接单及时率 -->
  <view class="padding-top-10 padding-bottom-5 text-font-13 text-color-textgray">
    <van-icon name="description-o" color="#1677ff" class="padding-right-5" />{{serviceReportTitle}}
  </view>
  <view class="view-d padding-bottom-10">
    <view wx:if="{{serviceReportinfo1loading}}" class="padding-tb-20 text-align-center">
      <van-loading size="24px" color="#1677ff">加载中...</van-loading>
    </view>
    <view wx:elif="{{serviceReportinfo1.length === 0}}" class="padding-tb-20 text-align-center">
      <van-empty description="暂无数据" image="search" />
    </view>
    <view wx:else>
      <view class="padding-top-10" wx:for="{{serviceReportinfo1}}" wx:key="index">
        <view class="padding-lr-10 padding-top-5 flex-row text-font-12 text-color-content">
          <view class="flex-item-2 text-color-content">{{item.label}}</view>
          <view class="flex-item-6">
            <progress color="#1677ff" percent="{{item.ratePercent}}" stroke-width="10" border-radius="5" />
          </view>
          <view class="flex-item-2 padding-left-10">
            {{item.value}}<text class="padding-lr-5">|</text>{{item.ratePercent}}%
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 接单超时率 -->
  <view class="padding-top-10 padding-bottom-5 text-font-13 text-color-textgray">
    <van-icon name="description-o" color="#1677ff" class="padding-right-5" />{{timeoutTitle}}
  </view>
  <view class="view-d padding-bottom-10">
    <van-tabs active="{{ sRactive1 }}" bind:change="onSRChange1" color="#1677ff" tab-class="text-font-12">
      <van-tab title="全部"></van-tab>
      <van-tab title="超1小时"></van-tab>
      <van-tab title="超2小时"></van-tab>
      <van-tab title="超3小时"></van-tab>
    </van-tabs>
    <view wx:if="{{serviceReportinfowloading}}" class="padding-tb-20 text-align-center">
      <van-loading size="24px" color="#1677ff">加载中...</van-loading>
    </view>
    <view wx:elif="{{serviceReportinfo2.length === 0}}" class="padding-tb-20 text-align-center">
      <van-empty description="暂无数据" image="search" />
    </view>
    <view wx:else>
      <view class="padding-top-10" wx:for="{{serviceReportinfo2}}" wx:key="index">
        <view class="padding-lr-10 padding-top-5 flex-row text-font-12 text-color-content">
          <view class="flex-item-2 text-color-content">{{item.label}}</view>
          <view class="flex-item-6">
            <progress color="#1677ff" percent="{{item.ratePercent}}" stroke-width="10" border-radius="5" />
          </view>
          <view class="flex-item-2 padding-left-10">
            {{item.value}}<text class="padding-lr-5">|</text>{{item.ratePercent}}%
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 统计图表 -->
  <view class="padding-top-10 padding-bottom-5 text-font-14 text-color-textgray">
    <van-icon name="description-o" color="#1677ff" class="padding-right-5" />统计
  </view>
  <view class="view-d">
    <view class="flex-row">
      <view class="{{shouldHideTabs ? 'flex-item-3' : 'flex-item-2'}}">
        <view class="padding-left-20 text-color-content" bindtap="showYearPopup">
          <text class="padding-right-5 text-font-14">{{selectedYear}}</text>
          <van-icon size="13" name="arrow-down" />
        </view>
      </view>
      <view class="flex-item-3" wx:if="{{!shouldHideTabs}}">
        <!-- 区域选择 -->
        <view class="padding-left-20 text-color-content" bindtap="showRegionPopup">
          <text class="padding-right-5 text-font-14">{{selectedRegion}}</text>
          <van-icon size="13" name="arrow-down" />
        </view>
      </view>
      <view class="{{shouldHideTabs ? 'flex-item-7' : 'flex-item-5'}}">
        <van-tabs active="{{ active }}" bind:change="ontjChange" color="#1677ff">
          <van-tab title="年度"></van-tab>
          <van-tab title="自然月"></van-tab>
          <van-tab title="自然周"></van-tab>
        </van-tabs>
      </view>
    </view>
    <view style="height: 600rpx;">
      <ec-canvas ec="{{ lineec }}"></ec-canvas>
    </view>
  </view>
</view>

<!-- 时间选择弹出层 -->
<van-popup show="{{ timeshow }}" position="bottom" custom-style="height: 40%;" bind:close="onClose">
  <van-datetime-picker
    type="date"
    title="请选择年月日"
    value="{{ currentDate }}"
    min-date="{{ minDate }}"
    max-date="{{ maxDate }}"
    bind:confirm="onConfirm"
    bind:cancel="onClose"
  />
</van-popup>

<!-- 年份选择弹出层 -->
<van-popup show="{{ yearshow }}" position="bottom" custom-style="height: 40%;" bind:close="onYearCancel">
  <van-picker
    show-toolbar
    title="请选择年份"
    columns="{{ yearColumns }}"
    default-index="{{ yearIndex }}"
    bind:confirm="onYearConfirm"
    bind:cancel="onYearCancel"
  />
</van-popup>

<!-- 区域选择弹出层 -->
<van-popup show="{{ regionshow }}" position="bottom" custom-style="height: 40%;" bind:close="onRegionClose">
  <van-picker
    show-toolbar
    title="请选择区域"
    columns="{{ regionColumns }}"
    default-index="{{ regionIndex }}"
    bind:confirm="onRegionConfirm"
    bind:cancel="onRegionCancel"
  />
</van-popup>

<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
