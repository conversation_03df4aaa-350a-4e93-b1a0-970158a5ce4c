var dateUtil = require("../../utils/dateUtil.js");
const request = require("../../utils/request.js");
var diyecharts = require("../../utils/diyecharts.js");
const rolePermission = require("../../utils/rolePermission.js");
const app = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    level:0,
    isnew: false, //判断是否新用户（未登录）
    timeshow: false,
    minDate: new Date(2015, 0, 1).getTime(),
    maxDate: new Date(2035, 11, 1).getTime(),
    currentDate: "",
    startTime: dateUtil.getLast30DaysStartDate(),
    endTime: dateUtil.getLast30DaysEndDate(),
    selectedYear: new Date().getFullYear(),
    active: 1, // 默认选中"年度"选项卡
    sractive: 0, //
    tType: 1,
    timeType: 1,
    title1: ["满意度汇总", "评价汇总"],
    title2: ["满意率", "评价率"],
    value: 0,
    option: [
      { text: "满意度分析", value: 0 },
      { text: "评价率分析", value: 1 },
    ],
    yearColumns: [
      {
        values: [
          "2020",
          "2021",
          "2022",
          "2023",
          "2024",
          "2025",
          "2026",
          "2027",
          "2028",
          "2029",
          "2030",
        ],
      },
    ],
    yearIndex: 0, // 初始值，将在onLoad中根据selectedYear计算
    yearshow: false,
    regioninfo: [],
    selectedRegion: "暂无",
    selectedRegionValue: "",
    regionIndex: 0,
    regionshow: false,
    pieec: {
      onInit: null,
    },
    lineec: {
      onInit: null,
    },

    summaryinfo: [],
    serviceReportinfo: [],
    serviceReportinfoloading: true,
  },
  //满意度
  SatisfactionGetSummary() {
   const ensurePieReady = diyecharts.createChartGuard(this,"chartInstancePie");
   diyecharts.showLoading(this,"chartInstancePie")
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };
    request
      .post({
        url: "Satisfaction/GetSummary",
        data,
        load: false,
      })
      .then(async(res) => {
        await ensurePieReady(); // 等待图表实例创建
        this.setData({
          summaryinfo: res.data,
        });
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
        diyecharts.updatePieChart(this,piedata,this.data.title1[this.data.value]);
      });
  },
  SatisfactionGetServiceReport() {
    this.setData({
      serviceReportinfoloading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
    };
    request
      .post({
        url: "Satisfaction/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data
          .filter((item) => item.label !== null) // 过滤掉label为null的项
        .map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
          };
        });

        this.setData({
          serviceReportinfo: processedData,
          serviceReportinfoloading: false
        });
      })
      .catch((err) => {
        console.error("获取满意率数据失败:", err);
        this.setData({
          serviceReportinfo: [],
          serviceReportinfoloading: false
        });
      });
  },
  SatisfactionGetStat() {
     const ensureLineReady = diyecharts.createChartGuard(this,"chartInstanceLine");
      diyecharts.showLoading(this,"chartInstanceLine")
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;

    // 使用全局权限工具获取value参数
    const value = rolePermission.getValueByRole(this.data.userInfo, this.data.selectedRegionValue);

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        value: value
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level: this.data.level,
        timeType: timeType,
        value: value
      };
    }
    request
      .post({
        url: "Satisfaction/GetStat",
        data,
        load: false,
      })
      .then(async (res) => {
         await ensureLineReady(); // 等待图表实例创建
        const linedata = [];
        const xAxisData = [];

        // 根据不同的 timeType 格式化标签
        if (timeType == 2) {
          // 自然月
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({ length: 12 }, (_, i) =>
            (i + 1).toString()
          );
          const monthData = {};

          // 初始化所有月份的数据为0
          allMonths.forEach((month) => {
            monthData[month] = 0;
          });

          // 填充已有的月份数据
          res.data.forEach((e) => {
            // 提取月份数字
            let month = e.label;
            if (month.includes("-")) {
              // 如果是"2025-11"这样的格式，提取月份部分
              month = month.split("-")[1];
            }
            // 去掉前导零
            month = parseInt(month).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            monthData[month] = rate;
          });

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 按月份顺序填充数据
          allMonths.forEach((month) => {
            xAxisData.push(month + "月");
            linedata.push(monthData[month]);
          });
        } else if (timeType == 3) {
          // 自然周
          // 对于自然周，也可以实现类似的排序和补齐逻辑
          // 这里先简单处理，只进行排序
          const weekData = [];

          res.data.forEach((e) => {
            let week = e.label;
            if (week.includes("-")) {
              // 如果是"2025-11"这样的格式，提取周数部分
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            weekData.push({
              week: week,
              rate: rate,
            });
          });

          // 按周数排序
          weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 填充排序后的数据
          weekData.forEach((item) => {
            xAxisData.push(item.week + "周");
            linedata.push(item.rate);
          });
        } else {
          // 其他类型（如年度）保持原样
          res.data.forEach((e) => {
            let formattedLabel = e.label;
            let rate = parseFloat((e.rate * 100).toFixed(12));
            xAxisData.push(formattedLabel);
            linedata.push(rate);
          });
        }

         diyecharts.updateLineChart(this,linedata, xAxisData, timeType,"满意率");
      });
  },
  //评价
  RatingRateGetSummary() {
  const ensurePieReady = diyecharts.createChartGuard(this,"chartInstancePie");
   diyecharts.showLoading(this,"chartInstancePie")
    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
    };
    request
      .post({
        url: "RatingRate/GetSummary",
        data,
        load: false,
      })
      .then(async(res) => {
         await ensurePieReady(); // 等待图表实例创建
        this.setData({
          summaryinfo: res.data,
        });
        const piedata = res.data
          .filter((e) => e.pieShowFlag) // 确保过滤有效数据
          .map((e) => ({
            value: e.quantity,
            name: e.title,
          }));
         diyecharts.updatePieChart(this,piedata,this.data.title1[this.data.value]);
      });
  },
  RatingRateGetServiceReport() {
    this.setData({
      serviceReportinfoloading: true
    });

    // 使用全局权限工具获取level参数
    const level = rolePermission.getLevelByRole(this.data.userInfo, this.data.sractive + 1);

    var data = {
      dateTime: [this.data.startTime, this.data.endTime],
      level: level,
    };
    request
      .post({
        url: "RatingRate/GetServiceReport",
        data,
        load: false,
      })
      .then((res) => {
        // 处理数据，计算并格式化百分比
        const processedData = res.data
          .filter((item) => item.label !== null) // 过滤掉label为null的项
        .map((item) => {
          // 确保数据是数值类型
          // 计算百分比（用于进度条，需要是数值）
          const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
          // 返回处理后的数据，添加格式化后的百分比
          return {
            ...item,
            // 数值形式的百分比，用于进度条
            ratePercent: ratePercentValue,
            // // 格式化为百分比字符串，用于显示
            // ratePercentText: ratePercentText
          };
        });

        this.setData({
          serviceReportinfo: processedData,
          serviceReportinfoloading: false
        });
      })
      .catch((err) => {
        console.error("获取评价率数据失败:", err);
        this.setData({
          serviceReportinfo: [],
          serviceReportinfoloading: false
        });
      });
  },
  RatingRateGetStat() {
     const ensureLineReady = diyecharts.createChartGuard(this,"chartInstanceLine");
      diyecharts.showLoading(this,"chartInstanceLine")
    var data = {};
    // 使用 this.data.timeType 而不是重新计算
    var timeType = this.data.timeType;

    // 使用全局权限工具获取value参数
    const value = rolePermission.getValueByRole(this.data.userInfo, this.data.selectedRegionValue);

    if (timeType == 1) {
      data = {
        level: this.data.level,
        timeType: timeType,
        value: value,
      };
    } else {
      data = {
        year: this.data.selectedYear,
        level: this.data.level,
        timeType: timeType,
        value: value,
      };
    }
    request
      .post({
        url: "RatingRate/GetStat",
        data,
        load: false,
      })
      .then(async(res) => {
          await ensureLineReady(); // 等待图表实例创建
        const linedata = [];
        const xAxisData = [];

        // 根据不同的 timeType 格式化标签
        if (timeType == 2) {
          // 自然月
          // 创建一个包含1-12月的完整数组
          const allMonths = Array.from({ length: 12 }, (_, i) =>
            (i + 1).toString()
          );
          const monthData = {};

          // 初始化所有月份的数据为0
          allMonths.forEach((month) => {
            monthData[month] = 0;
          });

          // 填充已有的月份数据
          res.data.forEach((e) => {
            // 提取月份数字
            let month = e.label;
            if (month.includes("-")) {
              // 如果是"2025-11"这样的格式，提取月份部分
              month = month.split("-")[1];
            }
            // 去掉前导零
            month = parseInt(month).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            monthData[month] = rate;
          });

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 按月份顺序填充数据
          allMonths.forEach((month) => {
            xAxisData.push(month + "月");
            linedata.push(monthData[month]);
          });
        } else if (timeType == 3) {
          // 自然周
          // 对于自然周，也可以实现类似的排序和补齐逻辑
          // 这里先简单处理，只进行排序
          const weekData = [];

          res.data.forEach((e) => {
            let week = e.label;
            if (week.includes("-")) {
              // 如果是"2025-11"这样的格式，提取周数部分
              week = week.split("-")[1];
            }
            // 去掉前导零
            week = parseInt(week).toString();

            let rate = parseFloat((e.rate * 100).toFixed(12));
            weekData.push({
              week: week,
              rate: rate,
            });
          });

          // 按周数排序
          weekData.sort((a, b) => parseInt(a.week) - parseInt(b.week));

          // 清空原数组
          xAxisData.length = 0;
          linedata.length = 0;

          // 填充排序后的数据
          weekData.forEach((item) => {
            xAxisData.push(item.week + "周");
            linedata.push(item.rate);
          });
        } else {
          // 其他类型（如年度）保持原样
          res.data.forEach((e) => {
            let formattedLabel = e.label;
            let rate = parseFloat((e.rate * 100).toFixed(12));
            xAxisData.push(formattedLabel);
            linedata.push(rate);
          });
        }

        diyecharts.updateLineChart(this,linedata, xAxisData, timeType,"评价率");
      });
  },
  RegionGetList() {
    request
      .get({
        url: "Region/GetList",
        load: false,
      })
      .then((res) => {
        if (res.data && res.data.length > 0) {
          // 获取区域数据
          const regions = res.data;

          // 创建区域选择列表
          const regionLabels = regions.map((item) => item.label);
          const regionColumns = [{ values: regionLabels }];

          // 默认选择第一个区域
          const firstRegion = regions[0].label;

          this.setData({
            regioninfo: regions,
            regionColumns: regionColumns,
            selectedRegion: firstRegion,
            selectedRegionValue: regions[0].value,
            regionIndex: 0,
          });

            this.init1();

        }
      });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 计算当前年份对应的yearIndex
    const currentYear = new Date().getFullYear().toString();
    const yearValues = this.data.yearColumns[0].values;
    const yearIndex = yearValues.indexOf(currentYear);

    // 如果找到当前年份在数组中的位置，则更新yearIndex
    if (yearIndex !== -1) {
      this.setData({
        yearIndex: yearIndex,
      });
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 先设置图表初始化函数，但不会立即执行
    this.setData(
      {
        pieec: {
          onInit: (canvas, width, height, dpr) => {
            // 立即保存实例引用
            this.chartInstancePie = diyecharts.initPieChart(
              this,
              canvas,
              width,
              height,
              dpr,

            );
            return this.chartInstancePie;
          },
        },
        lineec: {
          onInit: (canvas, width, height, dpr) => {
            // 立即保存实例引用
            this.chartInstanceLine = diyecharts.initLineChart(
              this,
              canvas,
              width,
              height,
              dpr
            );
            return this.chartInstanceLine;
          },
        },
        userInfo: wx.getStorageSync("userInfo"),
        isnew: app.globalData.isnew,
        level: app.globalData.level,
        shouldHideTabs: rolePermission.shouldHideTabs(wx.getStorageSync("userInfo"))
      },
      () => {
        if(!this.data.isnew){
          // 只有在已登录状态下才加载数据
          this.RegionGetList();
          this.init();
        }
      }
    );
  },

  // 登录按钮点击事件
  login() {
    wx.navigateTo({
      url: "/pages/login/login",
    });
  },
    onUnload() {
     diyecharts.onUnload(this)
    },
  init() {
    switch (this.data.value) {
      case 0:
        this.SatisfactionGetSummary();
        this.SatisfactionGetServiceReport();
        break;
      case 1:
        this.RatingRateGetSummary();
        this.RatingRateGetServiceReport();
        break;
      default:
        // 默认处理
        break;
    }
  },
  init1() {
    switch (this.data.value) {
      case 0:
        this.SatisfactionGetStat();
        break;
      case 1:
        this.RatingRateGetStat();
        break;
      default:
        // 默认处理
        break;
    }
  },

    //顶部选择日期范围的事件
  onOptionChange(e) {
    var value = e.detail;
    this.setData({
      value: value,
    });
    this.init();
    this.init1();
  },

  startTimePopup() {
    var time = new Date(this.data.startTime).getTime();
    this.setData({
      timeshow: true,
      currentDate: time,
      tType: 1,
    });
  },

  endTimePopup() {
    var time = new Date(this.data.endTime).getTime();
    this.setData({
      timeshow: true,
      currentDate: time,
      tType: 2,
    });
  },

  onConfirm(e) {
    console.log(e.detail);
    this.setData({
      timeshow: false,
      currentDate: e.detail,
    });
    if (this.data.tType == 1) {
      this.setData({
        startTime: dateUtil.getDateObject(e.detail),
      });
    } else {
      this.setData({
        endTime: dateUtil.getDateObject(e.detail),
      });
    }
    this.init();
  },

  onClose() {
    this.setData({
      timeshow: false,
    });
  },

  // 统计 事件

 // 显示年份选择弹出层

  ontjChange(e) {
    const index = e.detail.index;
    // 更新 active 和 timeType 值
    this.setData({
      active: index,
      timeType: index + 1, // 设置 timeType: 1-年度，2-自然月，3-自然周
    });
    // 切换选项卡后重新获取统计数据
      this.init1();
  },

  // 显示年份选择弹出层
  showYearPopup() {
    this.setData({
      yearshow: true,
    });
  },

  // 确认选择年份
  onYearConfirm(e) {
    const { value, index } = e.detail;
    const year = parseInt(value[0]);

    this.setData({
      selectedYear: year,
      yearIndex: index[0],
      yearshow: false,
    });

    // 选择年份后重新获取统计数据
    this.init1();
  },

  // 取消选择年份
  onYearCancel() {
    this.setData({
      yearshow: false,
    });
  },
  // 显示区域选择弹出层
  showRegionPopup() {
    this.setData({
      regionshow: true,
    });
  },

  // 确认选择区域
  onRegionConfirm(e) {
    const { value, index } = e.detail;
    const regionLabel = value[0];

    // 找到对应的区域对象
    const selectedRegion = this.data.regioninfo.find(
      (item) => item.label === regionLabel
    );

    if (selectedRegion) {
      this.setData({
        selectedRegion: selectedRegion.label,
        selectedRegionValue: selectedRegion.value,
        regionIndex: index[0],
        regionshow: false,
      });
      // 选择年份后重新获取统计数据
      this.init1();
    }
  },

  // 取消选择区域
  onRegionCancel() {
    this.setData({
      regionshow: false,
    });
  },

  // 关闭区域选择弹出层
  onRegionClose() {
    this.setData({
      regionshow: false,
    });
  },
  onsrChange(e) {
    // 获取点击的 tab 的 name 值，这个值是固定的（1-区域，2-城市，3-门店，4-工程师，5-调度员）
    const index = e.detail.index;
    this.setData({
      sractive: index, // 减1是因为 sractive 用于显示当前选中的 tab
    });
    // 切换选项卡后重新获取统计数据
    switch (this.data.value) {
      case 0:
        this.SatisfactionGetServiceReport();
        break;
      case 1:
        this.RatingRateGetServiceReport();
        break;
      default:
        // 默认处理
        break;
    }
  }
});
