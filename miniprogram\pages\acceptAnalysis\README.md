# 接单及时性分析页面

## 概述
这是一个从 `workorderAnalysis` 页面中提取出来的独立页面，专门用于接单及时性分析。

## 功能特性

### 1. 数据展示
- **工单汇总（饼图）**: 显示接单相关的工单统计数据
- **接单及时率**: 以进度条形式展示各维度的接单及时率
- **接单超时率**: 支持按时间类型筛选的超时率统计
- **统计图表（折线图）**: 按年度/月度/周度展示接单及时率趋势

### 2. 交互功能
- **时间范围选择**: 支持自定义开始和结束时间
- **权限级别切换**: 支持区域/城市/门店/工程师/调度员维度切换
- **超时时间筛选**: 支持全部/超1小时/超2小时/超3小时筛选
- **统计维度切换**: 支持年度/自然月/自然周统计
- **区域选择**: 支持选择不同区域进行统计

### 3. 权限控制
- **登录状态检查**: 未登录时显示登录提示
- **角色权限**: 根据用户角色显示/隐藏相应的功能模块
- **数据权限**: 根据用户权限级别获取对应的数据

## API接口

### 1. 汇总数据
- **接口**: `ServiceFormAccept/GetSummary`
- **参数**: `dateTime` (时间范围)
- **返回**: 工单汇总统计数据

### 2. 及时率数据
- **接口**: `ServiceFormAccept/GetServiceReport`
- **参数**: `dateTime`, `level`, `type: 1`
- **返回**: 接单及时率数据

### 3. 超时率数据
- **接口**: `ServiceFormAccept/GetServiceReport`
- **参数**: `dateTime`, `level`, `type: 2`, `timeType`
- **返回**: 接单超时率数据

### 4. 统计数据
- **接口**: `ServiceFormAccept/GetStat`
- **参数**: `level`, `timeType`, `value`, `year` (可选)
- **返回**: 统计图表数据

## 页面结构

```
acceptAnalysis/
├── index.js          # 页面逻辑
├── index.wxml        # 页面结构
├── index.wxss        # 页面样式
├── index.json        # 页面配置
└── README.md         # 说明文档
```

## 使用方式

### 1. 从工单分析页面跳转
在 `workorderAnalysis` 页面选择"接单及时性分析"时会自动跳转到此页面。

### 2. 直接访问
```javascript
wx.navigateTo({
  url: '/pages/acceptAnalysis/index'
});
```

## 数据处理

### 1. 百分比计算
```javascript
const ratePercentValue = parseFloat((item.rate * 100).toFixed(12));
```

### 2. 折线图数据处理
- **自然月**: 补齐1-12月数据，缺失月份显示为0
- **自然周**: 按周数排序显示
- **年度**: 直接显示原始数据

### 3. 权限参数处理
- 使用 `rolePermission.getLevelByRole()` 获取权限级别
- 使用 `rolePermission.getValueByRole()` 获取权限值

## 样式特性

- 响应式布局，适配不同屏幕尺寸
- 使用 Vant Weapp 组件库
- 支持加载状态和空状态显示
- 图表容器高度固定为 600rpx

## 注意事项

1. **图表初始化**: 需要等待图表实例创建完成后再更新数据
2. **权限控制**: 根据用户角色隐藏/显示相应功能
3. **数据过滤**: 过滤掉 `label` 为 `null` 的数据项
4. **错误处理**: 所有API调用都包含错误处理逻辑

## 依赖组件

- `nav-bar`: 自定义导航栏
- `ec-canvas`: 图表组件
- `@vant/weapp`: UI组件库

## 更新日志

- **v1.0.0**: 初始版本，从 workorderAnalysis 页面提取接单及时性分析功能
