{"pages": ["pages/index/index", "pages/dataOverview/index", "pages/userAnalysis/index", "pages/workorderAnalysis/index", "pages/satisfactionAnalysis/index", "pages/complaintAnalysis/index", "pages/login/login", "pages/login/agreement/agreement", "pages/login/privacypolicy/privacypolicy", "pages/isEnable/index"], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "小松鼠服务数据分析", "navigationBarBackgroundColor": "#ffffff", "navigationStyle": "custom"}, "tabBar": {"borderStyle": "black", "position": "bottom", "backgroundColor": "#FFFFFF", "color": "#8a8a8a", "selectedColor": "#1677ff", "list": [{"pagePath": "pages/dataOverview/index", "text": "数据概览", "iconPath": "/assets/images/data1.png", "selectedIconPath": "/assets/images/data1_re.png"}, {"pagePath": "pages/userAnalysis/index", "text": "用户分析", "iconPath": "/assets/images/data2.png", "selectedIconPath": "/assets/images/data2_re.png"}, {"pagePath": "pages/workorderAnalysis/index", "text": "工单分析", "iconPath": "/assets/images/data3.png", "selectedIconPath": "/assets/images/data3_re.png"}, {"pagePath": "pages/satisfactionAnalysis/index", "text": "满意度分析", "iconPath": "/assets/images/data4.png", "selectedIconPath": "/assets/images/data4_re.png"}, {"pagePath": "pages/complaintAnalysis/index", "text": "投诉分析", "iconPath": "/assets/images/data5.png", "selectedIconPath": "/assets/images/data5_re.png"}]}, "sitemapLocation": "sitemap.json", "usingComponents": {"nav-bar": "/components/nav-bar/index", "van-button": "@vant/weapp/button/index", "van-field": "@vant/weapp/field/index", "van-image": "@vant/weapp/image/index", "van-popup": "@vant/weapp/popup/index", "van-area": "@vant/weapp/area/index", "van-toast": "@vant/weapp/toast/index", "van-cell": "@vant/weapp/cell/index", "van-cell-group": "@vant/weapp/cell-group/index", "van-icon": "@vant/weapp/icon/index", "van-collapse": "@vant/weapp/collapse/index", "van-collapse-item": "@vant/weapp/collapse-item/index", "van-radio": "@vant/weapp/radio/index", "van-radio-group": "@vant/weapp/radio-group/index", "van-tab": "@vant/weapp/tab/index", "van-tabs": "@vant/weapp/tabs/index", "van-dialog": "@vant/weapp/dialog/index", "van-sticky": "@vant/weapp/sticky/index", "van-loading": "@vant/weapp/loading/index", "van-tabbar": "@vant/weapp/tabbar/index", "van-tabbar-item": "@vant/weapp/tabbar-item/index", "van-slider": "@vant/weapp/slider/index", "van-calendar": "@vant/weapp/calendar/index", "van-switch": "@vant/weapp/switch/index", "van-swipe-cell": "@vant/weapp/swipe-cell/index", "van-datetime-picker": "@vant/weapp/datetime-picker/index", "van-dropdown-menu": "@vant/weapp/dropdown-menu/index", "van-dropdown-item": "@vant/weapp/dropdown-item/index", "van-picker": "@vant/weapp/picker/index", "van-row": "@vant/weapp/row/index", "van-col": "@vant/weapp/col/index", "van-tag": "@vant/weapp/tag/index", "van-action-sheet": "@vant/weapp/action-sheet/index", "van-checkbox": "@vant/weapp/checkbox/index", "van-checkbox-group": "@vant/weapp/checkbox-group/index", "van-notice-bar": "@vant/weapp/notice-bar/index", "van-tree-select": "@vant/weapp/tree-select/index", "van-progress": "@vant/weapp/progress/index", "van-grid": "@vant/weapp/grid/index", "van-grid-item": "@vant/weapp/grid-item/index", "van-sidebar": "@vant/weapp/sidebar/index", "van-divider": "@vant/weapp/divider/index", "van-uploader": "@vant/weapp/uploader/index", "van-steps": "@vant/weapp/steps/index", "van-empty": "@vant/weapp/empty/index", "van-nav-bar": "@vant/weapp/nav-bar/index", "van-circle": "@vant/weapp/circle/index", "van-skeleton": "@vant/weapp/skeleton/index", "ec-canvas": "ec-canvas/ec-canvas"}, "requiredPrivateInfos": ["getLocation"], "__usePrivacyCheck__": true}