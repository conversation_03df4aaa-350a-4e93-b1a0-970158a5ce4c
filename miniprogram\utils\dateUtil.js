// utils/dateUtils.js

/**
 * 获取自定义日期
 * @param {Object} options 配置项
 * @param {number} [options.years=0] 需要加减的年数
 * @param {number} [options.months=0] 需要加减的月数
 * @param {number} [options.days=0] 需要加减的天数
 * @returns {string} 格式化后的日期字符串 YYYY-MM-DD
 */
export function getCustomDate(options = { years: 0, months: 0, days: 0 }) {
  const { years, months, days } = options;
  const date = new Date();

  // 设置年月日加减
  date.setFullYear(date.getFullYear() + years);
  date.setMonth(date.getMonth() + months);
  date.setDate(date.getDate() + days);

  // 格式化数字补零
  const formatNumber = n => n.toString().padStart(2, '0');

  const year = date.getFullYear();
  const month = formatNumber(date.getMonth() + 1); // 月份从0开始需要+1
  const day = formatNumber(date.getDate());

  return `${year}-${month}-${day}`;
}
// 获取当前年份的1月1日
export function getYearStartDate() {
  const today = new Date();
  return getCustomDate({
    months: -today.getMonth(),     // 回退到1月
    days: -(today.getDate() - 1)  // 回退到1号
  });
}
// 默认获取当前日期（不加不减）
export function getCurrentDate() {
  return getCustomDate();
}

// 获取最近30天的开始日期（30天前的日期）
export function getLast30DaysStartDate() {
  var date = new Date();
  date.setDate(date.getDate() - 30); // 获取29天前的日期（加上今天共30天）
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
}

//获取最近30天的结束日期（当前日期的前一天）
export function getLast30DaysEndDate() {
  var date = new Date();
  date.setDate(date.getDate() - 1); // 获取昨天的日期
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
}

export function getStartYearDate() {
 var mate= new Date(new Date().getFullYear(), 0, 1).getTime()
  const date = new Date(mate)
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
}
export function getEndYearDate() {
  var mate= new Date(new Date().getFullYear()+1, 0, 1).getTime()
   const date = new Date(mate)
   return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
 }

// 获取当前月的第一天
export function getStartMonthDate() {
  var mate = new Date(new Date().getFullYear(), new Date().getMonth(), 1).getTime()
  const date = new Date(mate)
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
}

// 获取下个月的第一天（作为当前月的结束日期）
export function getEndMonthDate() {
  var mate = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1).getTime()
  const date = new Date(mate)
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
}

 export function getDateObject(timestamp) {
// 将时间戳转换为 Date 对象
const dateObject = new Date(timestamp);

console.log("Date Object:", dateObject);

// 将 Date 对象格式化为 YYYY-MM-DD 格式的字符串
const year = dateObject.getFullYear();
const month = String(dateObject.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
const day = String(dateObject.getDate()).padStart(2, '0');

return `${year}-${month}-${day}`;
 }