<van-sticky>
  <nav-bar title="小松鼠服务数据分析" />
  <view class="text-font-14 flex-row bg-color-white text-align-center padding-tb-10" style="justify-content: left; ">
    <view class="padding-left-10">
      <van-icon name="notes-o" size="18px" />
    </view>
    <view class="padding-left-5" bindtap="startTimePopup">{{startTime}}</view>
    <view class="padding-left-5">~</view>
    <view class="padding-left-5" bindtap="endTimePopup">{{endTime}}</view>
  </view>
</van-sticky>
<view class="padding-tb-10 padding-lr-10 flex-row" wx:if="{{isnew}}">
  <view class="flex-item-6 text-font-14">查看数据请前往登录...</view>
  <view class="flex-item-4 text-align-right">
    <van-button type="info" size="small" bind:tap="login">登录</van-button>
  </view>
</view>
<view class="padding-lr-10">
  <view class="padding-top-10 padding-bottom-5 text-font-14 text-color-textgray">
    <van-icon name="description-o" color="#1677ff" class="padding-right-5" />工单概况
  </view>
  <view class="view-d">
    <van-skeleton row="6" loading="{{ summaryLoading }}">
    <view wx:if="{{summaryinfo.length > 0}}">
      <view class="text-font-10">
        <van-grid column-num="3">
          <van-grid-item use-slot wx:for="{{summaryinfo}}" wx:key="index">
            <view>
              <view class="">{{item.title}}</view>
              <view class="padding-tb-5 text-font-bold"><text class="text-font-20">{{item.quantity}}</text> 单</view>
              <view class="text-color-content"> 同比上期
                <text class="text-color-red" wx:if="{{item.rate<0}}">{{item.rate}}%</text>
                <text class="text-color-success" wx:else>+{{item.rate}}%</text>
              </view>
            </view>
          </van-grid-item>
        </van-grid>
      </view>
    </view>
    <view wx:else>
      <view style="height: auto; display: flex; align-items: center; justify-content: center;">
        <van-empty  description="暂无数据" />
      </view>
    </view>
  </van-skeleton>
  </view>
  <view class="padding-tb-5 text-font-14 text-color-textgray">
    <van-icon name="description-o" color="#1677ff" class="padding-right-5" />关键KPI
  </view>
  <view class="view-d">
    <van-skeleton row="10" loading="{{ channelinfoloading }}">
    <view wx:if="{{channelinfo.length > 0 && !timeshow}}">
      <view class="text-font-10">
        <van-grid column-num="2">
          <van-grid-item use-slot wx:for="{{channelinfo}}" wx:key="index">
            <view class="text-align-center">
              <van-circle value="{{ item.rate * 100 }}" size="90" stroke-width="10" text="{{item.rateText}}" color="{{item.color}}" layer-color="#f2f2f2" />
              <view class="text-font-12 padding-tb-5">{{item.title}}</view>
            </view>
          </van-grid-item>
        </van-grid>
      </view>
    </view>
    <view wx:else>
      <view style="height: auto; display: flex; align-items: center; justify-content: center;">
        <van-empty  description="暂无数据" />
      </view>
    </view>
   </van-skeleton>
  </view>
  <view class="padding-tb-5 text-font-14 text-color-textgray">
    <van-icon name="description-o" color="#1677ff" class="padding-right-5" />关键KPI去年同期对比
  </view>
  <view class="view-d">
    <view style=" height: 600rpx; " wx:if="{{barec}}">
      <ec-canvas ec="{{ barec }}"></ec-canvas>
    </view>
    <view wx:else>
      <view style="height: auto; display: flex; align-items: center; justify-content: center;">
        <van-empty description="暂无数据" />
      </view>
    </view>
  </view>
</view>
<van-popup show="{{ timeshow }}" position="bottom" custom-style="height: 40%;" bind:close="onClose">
  <van-datetime-picker type="date" title="请选择年月日" value="{{ currentDate }}" min-date="{{ minDate }}" max-date="{{ maxDate }}" bind:confirm="onConfirm" bind:cancel="onClose" />
</van-popup>

<van-toast id="van-toast" />
<van-dialog id="van-dialog" />