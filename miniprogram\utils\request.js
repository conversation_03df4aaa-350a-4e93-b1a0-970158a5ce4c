const toastDialog = require('./toastDialog.js')
const app = getApp()
var baseUrl = app.globalData.url
let isRefreshing = false; // 刷新 Token 状态
let refreshTokenPromise = null; // 存储刷新 Token 的 Promise 队列
const requestMap = new Map();
const defaults = {
  url: '',
  data: {},
  headers: {},
  method: 'GET',
  dataType: 'json',
  load: true,
  loadmsg: "加载中..."
};
/**
 * 封装 wx.request
 * @param {String} url - 请求的URL（相对路径）
 * @param {Object} options - 请求选项
 * @returns {Promise} - 返回一个 Promise 对象
 */
const request = (options) => {
  const settings = Object.assign({}, defaults, options);
  if (settings.load) {
    toastDialog.Tloading(settings.loadmsg);
  }
  const url = baseUrl + settings.url;
  // // 防止重复请求
  // if (requestMap.has(url)) {
  //   return Promise.reject("重复请求，请稍后再试");
  // }
  requestMap.set(url, true);
  return new Promise((resolve, reject) => {
    const makeRequest = () => {
      // // 待加密的数据
      // const data = '312313131313';
      // // 进行SHA-256加密
      // const encryptedData = SHA256(data);
      // console.log('加密后的数据：', encryptedData);
      wx.request({
        url,
        method: settings.method,
        data: settings.data,
        header: Object.assign({}, settings.headers, {
          authorization: 'Bearer ' + app.globalData.token, // 动态添加 Token
        }),
        dataType: settings.dataType,
        success: (res) => {
          //状态200,执行正常
          switch (res.data.code) {
            case 200:
              if (resolve)
                resolve(res.data)
              break;
            case 201:
              toastDialog.DialogAlert(res.data.msg, () => {
                reject(res.data.msg)
              })
              break;
            case 401:
              // 处理 Token 过期
              refreshToken()
                .then((newToken) => {
                  // 更新请求头，重新发起原请求
                  settings.headers.authorization = 'Bearer ' + newToken;
                  return request(settings); // 递归调用 request
                })
                .catch((err) => {
                  // 刷新失败，跳转登录
                  toastDialog.DialogAlert("登录已过期，请重新登录", () => {
                    wx.reLaunch({
                      url: '/pages/login/login'
                    })
                  });
                  reject(err);
                });
              break;
            default:
              toastDialog.DialogAlert("服务端繁忙，请稍后再试", () => {
                reject("服务端繁忙，请稍后再试")
              })
          }
        },
        fail: (err) => {
          toastDialog.DialogAlert("当前网络较差，请检查", () => {
            reject("当前网络较差，请检查")
          })
        },
        complete: (res) => {
          requestMap.delete(url);
          if (settings.load) {
            toastDialog.Tclear()
          }
        }
      });
    } // 开始发起请求
    makeRequest();
  });

}

/**
 * 刷新 Token 方法
 * @returns {Promise} - 返回一个 Promise，成功时返回新的 Token
 */
const refreshToken = () => {
  if (!isRefreshing) {
    isRefreshing = true;
    refreshTokenPromise = new Promise((resolve, reject) => {
      wx.login({
        success: (reslogin) => {
          wx.request({
            url: `${baseUrl}SysAccount/userIsAny`, // 刷新 Token 的接口
            method: 'get',
            data: {
              code: reslogin.code
            }, // 传入 Refresh Token
            success: (res) => {
              if (res.data.code == 200) {
                app.globalData.token = res.data.data.token; // 更新全局 Token
                resolve(res.data.data.token);
              } else {
                reject("刷新 Token 失败");
              }
            },
            fail: (err) => {
              reject(err);
            },
            complete: () => {
              isRefreshing = false;
            },
          });
        },
      })
    });
  }
  return refreshTokenPromise;
};


/**
 * GET 请求
 * @param {String} url - 请求的URL（相对路径）
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回一个 Promise 对象
 */
const get = (options) => {
  options.method = "GET"
  return request(options);
};
/**
 * POST 请求
 * @param {String} url - 请求的URL（相对路径）
 * @param {Object} data - 请求数据
 * @returns {Promise} - 返回一个 Promise 对象
 */
const post = (options) => {
  options.method = "POST"
  return request(options);
};
/**
 * DELETE 请求
 * @param {String} url - 请求的URL（相对路径）
 * @param {Object} data - 请求数据
 * @returns {Promise} - 返回一个 Promise 对象
 */
const del = (options) => {
  options.method = "DELETE"
  return request(options);
};
/**
 * PUT 请求
 * @param {String} url - 请求的URL（相对路径）
 * @param {Object} params - 请求参数
 * @returns {Promise} - 返回一个 Promise 对象
 */
const put = (options) => {
  options.method = "PUT"
  return request(options);
};
// 导出请求方法
module.exports = {
  get,
  post,
  del,
  put
};