/**app.wxss**/
@import "assets/css/style.wxss";
page {
  background-color:#f3f3f3
}
.main-page-view{
  padding: 30rpx;
}
.van-dropdown-menu{
  box-shadow: var(--dropdown-menu-box-shadow,0 2px 12px rgba(255, 255, 255, 0.12)) !important;
  height:auto !important;
  padding: 10rpx 0;
}
.view-d{
  background-color: white;
  border-radius: 10rpx;
  margin: 10rpx 0;
}
.container {
  height: 600rpx; /* 使用视口高度 */
}
.sxuan-view{
  font-size: 26rpx;
  color: #515a6e;
}
.sxuan-view .sxuan-view-btn{
    float: left;
    width: auto;
    border: 1px solid #ccc;
    padding: 6rpx 20rpx;
    border-radius: 10rpx;
    font-size: 24rpx;
    margin-right: 20rpx;
}